"""
BLU邊緣塗膠智能點膠系統主程式
整合所有模組，提供完整的BLU邊緣塗膠解決方案
包含即時視覺化和PID控制監控
"""

import numpy as np
import logging
import time
from typing import Dict, List, Tuple
import argparse
import matplotlib.pyplot as plt
import matplotlib.animation as animation
from matplotlib.patches import Circle
import threading
import queue
from datetime import datetime
import tkinter as tk
from tkinter import ttk
import matplotlib.backends.backend_tkagg as tkagg
from matplotlib.figure import Figure
import matplotlib.font_manager as fm
import platform

# 設置中文字體
def setup_chinese_font():
    """設置matplotlib中文字體"""
    system = platform.system()

    if system == "Windows":
        # Windows系統常用中文字體
        chinese_fonts = ['Microsoft YaHei', 'SimHei', 'SimSun', 'KaiTi']
    elif system == "Darwin":  # macOS
        chinese_fonts = ['PingFang SC', 'Heiti SC', 'STHeiti', 'Arial Unicode MS']
    else:  # Linux
        chinese_fonts = ['WenQuanYi Micro Hei', 'DejaVu Sans', 'Liberation Sans']

    # 嘗試設置中文字體
    for font_name in chinese_fonts:
        try:
            plt.rcParams['font.sans-serif'] = [font_name]
            plt.rcParams['axes.unicode_minus'] = False  # 解決負號顯示問題
            print(f"✅ 成功設置字體: {font_name}")
            return True
        except:
            continue

    # 如果都失敗，使用默認設置
    print("⚠️ 無法找到中文字體，使用默認字體")
    plt.rcParams['axes.unicode_minus'] = False
    return False

# 初始化字體設置
setup_chinese_font()

# 導入系統模組
from src.core.dispensing_system import BLUEdgeDispensingSystem, DispensingSpec
from src.algorithms.height_detection import HeightDetectionSystem, SurfaceAnalyzer, CompensationCalculator
from src.control.edge_coating_controller import EdgeCoatingController
from src.monitoring.surface_monitor import SurfaceMonitor
from src.quality.tolerance_control import ToleranceController, ToleranceSpec

# 配置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('blu_dispensing.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# PID控制器類
class PIDController:
    """PID控制器實現"""

    def __init__(self, kp=1.2, ki=0.1, kd=0.05):
        self.kp = kp
        self.ki = ki
        self.kd = kd
        self.previous_error = 0
        self.integral = 0
        self.last_time = time.time()

    def update(self, setpoint, current_value):
        """更新PID控制器"""
        current_time = time.time()
        delta_time = current_time - self.last_time

        if delta_time <= 0:
            delta_time = 0.001

        error = setpoint - current_value

        # 比例項
        proportional = self.kp * error

        # 積分項
        self.integral += error * delta_time
        integral = self.ki * self.integral

        # 微分項
        derivative = self.kd * (error - self.previous_error) / delta_time

        # PID輸出
        output = proportional + integral + derivative

        self.previous_error = error
        self.last_time = current_time

        return {
            'output': output,
            'error': error,
            'proportional': proportional,
            'integral': integral,
            'derivative': derivative
        }

    def reset(self):
        """重置PID控制器"""
        self.previous_error = 0
        self.integral = 0
        self.last_time = time.time()

    def set_parameters(self, kp, ki, kd):
        """設置PID參數"""
        self.kp = kp
        self.ki = ki
        self.kd = kd

# 視覺化監控系統
class VisualizationSystem:
    """即時視覺化監控系統"""

    def __init__(self):
        self.root = tk.Tk()
        self.root.title("BLU邊緣塗膠PID控制即時監控系統")
        self.root.geometry("1400x900")

        # 數據隊列
        self.data_queue = queue.Queue()
        self.is_running = False

        # 數據存儲
        self.height_data = []
        self.pid_history = []
        self.robot_path = []
        self.current_position = 0

        # 創建GUI
        self.setup_gui()

    def setup_gui(self):
        """設置GUI界面"""
        # 設置字體
        try:
            # 嘗試使用中文字體
            chinese_font = ('Microsoft YaHei', 10)
            bold_font = ('Microsoft YaHei', 12, 'bold')
        except:
            # 備用字體
            chinese_font = ('Arial', 10)
            bold_font = ('Arial', 12, 'bold')

        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 控制面板
        control_frame = ttk.LabelFrame(main_frame, text="控制參數", padding=10)
        control_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))

        # PID參數
        ttk.Label(control_frame, text="PID控制參數", font=bold_font).pack(anchor=tk.W)

        self.kp_var = tk.DoubleVar(value=1.2)
        self.ki_var = tk.DoubleVar(value=0.1)
        self.kd_var = tk.DoubleVar(value=0.05)

        ttk.Label(control_frame, text="Kp (比例增益):").pack(anchor=tk.W)
        ttk.Scale(control_frame, from_=0.1, to=5.0, variable=self.kp_var,
                 orient=tk.HORIZONTAL, length=200).pack(anchor=tk.W)
        ttk.Label(control_frame, textvariable=self.kp_var).pack(anchor=tk.W)

        ttk.Label(control_frame, text="Ki (積分增益):").pack(anchor=tk.W)
        ttk.Scale(control_frame, from_=0.01, to=1.0, variable=self.ki_var,
                 orient=tk.HORIZONTAL, length=200).pack(anchor=tk.W)
        ttk.Label(control_frame, textvariable=self.ki_var).pack(anchor=tk.W)

        ttk.Label(control_frame, text="Kd (微分增益):").pack(anchor=tk.W)
        ttk.Scale(control_frame, from_=0.01, to=1.0, variable=self.kd_var,
                 orient=tk.HORIZONTAL, length=200).pack(anchor=tk.W)
        ttk.Label(control_frame, textvariable=self.kd_var).pack(anchor=tk.W)

        # 目標參數
        ttk.Separator(control_frame, orient=tk.HORIZONTAL).pack(fill=tk.X, pady=10)
        ttk.Label(control_frame, text="目標參數", font=bold_font).pack(anchor=tk.W)

        self.target_height_var = tk.DoubleVar(value=1.2)
        self.target_width_var = tk.DoubleVar(value=1.4)

        ttk.Label(control_frame, text="目標高度 (mm):").pack(anchor=tk.W)
        ttk.Scale(control_frame, from_=0.8, to=1.6, variable=self.target_height_var,
                 orient=tk.HORIZONTAL, length=200).pack(anchor=tk.W)
        ttk.Label(control_frame, textvariable=self.target_height_var).pack(anchor=tk.W)

        # 狀態顯示
        ttk.Separator(control_frame, orient=tk.HORIZONTAL).pack(fill=tk.X, pady=10)
        ttk.Label(control_frame, text="即時狀態", font=bold_font).pack(anchor=tk.W)

        self.status_frame = ttk.Frame(control_frame)
        self.status_frame.pack(fill=tk.X)

        self.current_pos_label = ttk.Label(self.status_frame, text="當前位置: 待機")
        self.current_pos_label.pack(anchor=tk.W)

        self.speed_label = ttk.Label(self.status_frame, text="機器人速度: 0 mm/s")
        self.speed_label.pack(anchor=tk.W)

        self.flow_label = ttk.Label(self.status_frame, text="塗膠流量: 0 cm³/s")
        self.flow_label.pack(anchor=tk.W)

        self.error_label = ttk.Label(self.status_frame, text="高度誤差: 0 mm")
        self.error_label.pack(anchor=tk.W)

        self.progress_label = ttk.Label(self.status_frame, text="完成進度: 0%")
        self.progress_label.pack(anchor=tk.W)

        # 進度條
        self.progress_bar = ttk.Progressbar(control_frame, length=200, mode='determinate')
        self.progress_bar.pack(anchor=tk.W, pady=5)

        # 視覺化面板
        viz_frame = ttk.Frame(main_frame)
        viz_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        # 創建matplotlib圖表
        self.fig = Figure(figsize=(12, 8), dpi=100)
        self.canvas = tkagg.FigureCanvasTkAgg(self.fig, viz_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        # 子圖
        self.ax1 = self.fig.add_subplot(2, 2, 1)  # 高度分佈圖
        self.ax2 = self.fig.add_subplot(2, 2, 2)  # PID響應圖
        self.ax3 = self.fig.add_subplot(2, 2, 3)  # 機器人路徑圖
        self.ax4 = self.fig.add_subplot(2, 2, 4)  # 流量控制圖

        self.fig.tight_layout()

    def update_display(self, data):
        """更新顯示"""
        try:
            # 更新狀態標籤
            if 'current_position' in data:
                pos = data['current_position']
                self.current_pos_label.config(text=f"當前位置: ({pos[0]:.1f}, {pos[1]:.1f})")

            if 'robot_speed' in data:
                self.speed_label.config(text=f"機器人速度: {data['robot_speed']:.1f} mm/s")

            if 'flow_rate' in data:
                self.flow_label.config(text=f"塗膠流量: {data['flow_rate']:.2f} cm³/s")

            if 'height_error' in data:
                self.error_label.config(text=f"高度誤差: {data['height_error']:.3f} mm")

            if 'progress' in data:
                progress = data['progress']
                self.progress_label.config(text=f"完成進度: {progress:.1f}%")
                self.progress_bar['value'] = progress

            # 更新圖表
            self.update_charts(data)

        except Exception as e:
            logger.error(f"更新顯示失敗: {e}")

    def update_charts(self, data):
        """更新圖表"""
        # 清除所有子圖
        self.ax1.clear()
        self.ax2.clear()
        self.ax3.clear()
        self.ax4.clear()

        # 更新高度分佈圖
        if 'height_data' in data:
            self.plot_height_distribution(data['height_data'])

        # 更新PID響應圖
        if 'pid_data' in data:
            self.plot_pid_response(data['pid_data'])

        # 更新機器人路徑圖
        if 'robot_path' in data:
            self.plot_robot_path(data['robot_path'], data.get('current_position', 0))

        # 更新流量控制圖
        if 'flow_history' in data:
            self.plot_flow_control(data['flow_history'])

        self.canvas.draw()

    def plot_height_distribution(self, height_data):
        """繪製高度分佈圖"""
        if not height_data:
            return

        angles = [point['angle'] for point in height_data]
        heights = [point['height'] for point in height_data]

        # 極座標圖
        self.ax1 = self.fig.add_subplot(2, 2, 1, projection='polar')
        scatter = self.ax1.scatter(angles, heights, c=heights, cmap='coolwarm', s=30)
        self.ax1.set_title('BLU表面高度分佈', fontsize=12, pad=20)
        self.ax1.set_ylim(0.5, 1.5)

        # 添加顏色條
        try:
            cbar = self.fig.colorbar(scatter, ax=self.ax1, shrink=0.8)
            cbar.set_label('高度 (mm)', fontsize=10)
        except:
            pass  # 如果colorbar創建失敗，跳過

    def plot_pid_response(self, pid_data):
        """繪製PID響應圖（包含補償前後誤差對比）"""
        if len(pid_data) < 2:
            return

        times = [d['time'] for d in pid_data]
        outputs = [d['output'] for d in pid_data]
        compensated_errors = [d['error'] for d in pid_data]  # 補償後誤差
        raw_errors = [d.get('raw_error', d['error']) for d in pid_data]  # 補償前原始誤差

        # 繪製趨勢線
        self.ax2.plot(times, raw_errors, 'orange', linewidth=2.5, label='補償前誤差', alpha=0.8)
        self.ax2.plot(times, compensated_errors, 'r-', linewidth=2, label='補償後誤差')
        self.ax2.plot(times, outputs, 'b-', linewidth=2, label='PID輸出')

        # 添加零線參考
        self.ax2.axhline(y=0, color='k', linestyle='--', alpha=0.5, label='目標線')

        # 填充改善區域
        if len(times) > 1:
            improvement = [abs(raw) - abs(comp) for raw, comp in zip(raw_errors, compensated_errors)]
            positive_improvement = [max(0, imp) for imp in improvement]
            self.ax2.fill_between(times, 0, positive_improvement, alpha=0.2, color='green', label='改善效果')

        self.ax2.set_title('PID控制響應 (補償前後對比)', fontsize=12, pad=20)
        self.ax2.set_xlabel('時間 (s)', fontsize=10)
        self.ax2.set_ylabel('誤差/輸出值', fontsize=10)
        self.ax2.legend(fontsize=8, loc='upper right')
        self.ax2.grid(True, alpha=0.3)

        # 添加統計信息
        if raw_errors and compensated_errors:
            avg_raw_error = np.mean([abs(e) for e in raw_errors])
            avg_comp_error = np.mean([abs(e) for e in compensated_errors])
            improvement_rate = (avg_raw_error - avg_comp_error) / avg_raw_error * 100 if avg_raw_error > 0 else 0

            stats_text = f'平均改善: {improvement_rate:.1f}%\n原始誤差: {avg_raw_error:.3f}mm\n補償後: {avg_comp_error:.3f}mm'
            self.ax2.text(0.02, 0.98, stats_text, transform=self.ax2.transAxes,
                         verticalalignment='top', fontsize=8,
                         bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

    def plot_robot_path(self, robot_path, current_pos):
        """繪製機器人路徑圖"""
        if not robot_path:
            return

        # 繪製BLU輪廓
        circle = Circle((0, 0), 100, fill=False, color='black', linewidth=2)
        self.ax3.add_patch(circle)

        # 繪製路徑
        x_coords = [point['x'] - 250 for point in robot_path]  # 調整座標原點
        y_coords = [point['y'] - 150 for point in robot_path]

        # 已完成路徑
        if current_pos > 0:
            self.ax3.plot(x_coords[:current_pos], y_coords[:current_pos],
                         'g-', linewidth=3, label='已完成')

        # 未完成路徑
        if current_pos < len(x_coords):
            self.ax3.plot(x_coords[current_pos:], y_coords[current_pos:],
                         'gray', linestyle='--', alpha=0.5, label='待完成')

        # 當前位置
        if 0 <= current_pos < len(x_coords):
            self.ax3.plot(x_coords[current_pos], y_coords[current_pos],
                         'ro', markersize=10, label='當前位置')

        self.ax3.set_title('機器人移動路徑', fontsize=12, pad=20)
        self.ax3.set_xlabel('X (mm)', fontsize=10)
        self.ax3.set_ylabel('Y (mm)', fontsize=10)
        self.ax3.set_aspect('equal')
        self.ax3.legend(fontsize=9)
        self.ax3.grid(True, alpha=0.3)

    def plot_flow_control(self, flow_history):
        """繪製流量控制圖"""
        if len(flow_history) < 2:
            return

        times = [d['time'] for d in flow_history]
        flows = [d['flow'] for d in flow_history]
        stages = [d['stage'] for d in flow_history]

        # 流量曲線
        self.ax4.plot(times, flows, 'b-', linewidth=2, label='流量')

        # 段數指示
        colors = ['lightblue', 'orange', 'red']
        for i, stage in enumerate(stages):
            if i < len(times):
                self.ax4.axvline(x=times[i], color=colors[stage-1], alpha=0.3)

        self.ax4.set_title('塗膠流量控制', fontsize=12, pad=20)
        self.ax4.set_xlabel('時間 (s)', fontsize=10)
        self.ax4.set_ylabel('流量 (cm³/s)', fontsize=10)
        self.ax4.legend(fontsize=9)
        self.ax4.grid(True, alpha=0.3)

    def start(self):
        """啟動視覺化系統"""
        self.is_running = True
        self.update_loop()
        self.root.mainloop()

    def update_loop(self):
        """更新循環"""
        try:
            while not self.data_queue.empty():
                data = self.data_queue.get_nowait()
                self.update_display(data)
        except queue.Empty:
            pass

        if self.is_running:
            self.root.after(100, self.update_loop)

    def stop(self):
        """停止視覺化系統"""
        self.is_running = False
        self.root.quit()

class BLUDispensingApplication:
    """BLU點膠應用程式主類（含視覺化）"""

    def __init__(self, enable_visualization=True):
        """初始化應用程式"""
        # 系統規格
        self.dispensing_spec = DispensingSpec(
            width=1.4,
            height=1.2,
            tolerance=0.2,
            surface_flatness_requirement=0.05
        )

        self.tolerance_spec = ToleranceSpec(
            target_width=1.4,
            target_height=1.2,
            tolerance=0.2,
            flatness_requirement=0.05,
            adhesion_strength_min=0.8
        )

        # 初始化各個系統模組
        self.dispensing_system = BLUEdgeDispensingSystem(self.dispensing_spec)
        self.height_detector = HeightDetectionSystem(measurement_resolution=0.01)
        self.surface_analyzer = SurfaceAnalyzer(target_flatness=0.05)
        self.compensation_calculator = CompensationCalculator({
            'density': 1.2,
            'viscosity': 5000,
            'flow_factor': 0.8,
            'shrinkage': 0.02
        })
        self.coating_controller = EdgeCoatingController(
            target_width=1.4,
            target_height=1.2,
            tolerance=0.2
        )
        self.surface_monitor = SurfaceMonitor(
            flatness_threshold=0.05,
            sampling_rate=10.0
        )
        self.tolerance_controller = ToleranceController(self.tolerance_spec)

        # PID控制器
        self.pid_controller = PIDController(kp=1.2, ki=0.1, kd=0.05)

        # 視覺化系統
        self.enable_visualization = enable_visualization
        self.visualization_system = None
        if enable_visualization:
            self.visualization_system = VisualizationSystem()

        # 數據記錄
        self.pid_history = []
        self.flow_history = []
        self.robot_path_data = []
        self.raw_error_history = []  # 補償前的原始誤差
        self.start_time = None

        logger.info("BLU邊緣塗膠系統初始化完成（含視覺化）")
    
    def setup_hardware_callbacks(self):
        """設置硬體回調函數（模擬）"""
        def position_callback(command, *args):
            """位置控制回調"""
            if command == "move_to":
                x, y, z = args
                logger.debug(f"移動到位置: ({x:.3f}, {y:.3f}, {z:.3f})")
                time.sleep(0.1)  # 模擬移動時間
                return True
            elif command == "emergency_stop":
                logger.warning("緊急停止位置控制")
                return True
            return False
        
        def pressure_callback(command, *args):
            """壓力控制回調"""
            if command == "set_pressure":
                pressure = args[0]
                logger.debug(f"設置壓力: {pressure:.2f} bar")
                return True
            return False
        
        def flow_callback(command, *args):
            """流量控制回調"""
            if command == "set_flow_rate":
                flow_rate = args[0]
                logger.debug(f"設置流量: {flow_rate:.2f} ml/min")
                return True
            elif command == "start_dispensing":
                logger.debug("開始點膠")
                return True
            elif command == "stop_dispensing":
                logger.debug("停止點膠")
                return True
            elif command == "emergency_stop":
                logger.warning("緊急停止流量控制")
                return True
            return False
        
        def sensor_callback(command):
            """感測器回調（模擬數據）"""
            if command == "get_surface_data":
                # 模擬表面數據
                x_coords = np.linspace(0, 10, 50)
                y_coords = np.linspace(0, 10, 50)
                height_map = np.random.normal(1.2, 0.1, 50)  # 模擬高度數據
                
                return {
                    'height_map': height_map,
                    'coordinates': np.column_stack([x_coords, y_coords]),
                    'temperature': 25.0 + np.random.normal(0, 1),
                    'humidity': 50.0 + np.random.normal(0, 5)
                }
            return None
        
        # 設置回調函數
        self.coating_controller.set_hardware_callbacks(
            position_callback, pressure_callback, flow_callback
        )
        self.surface_monitor.set_callbacks(sensor_callback)
        
        logger.info("硬體回調函數設置完成")

    def run_complete_process_with_visualization(self, height_data_file: str = None) -> Dict:
        """執行完整的點膠流程（含視覺化）"""
        logger.info("開始執行完整的BLU邊緣塗膠流程（含視覺化）")

        if self.enable_visualization and self.visualization_system:
            # 在新線程中啟動視覺化系統
            viz_thread = threading.Thread(target=self.visualization_system.start, daemon=True)
            viz_thread.start()
            time.sleep(1)  # 等待GUI初始化

        try:
            self.start_time = time.time()

            # 步驟1: 載入或生成高度數據
            self.update_visualization("開始掃描BLU表面高度...", "info")
            if height_data_file:
                height_data, coordinates = self._load_height_data(height_data_file)
            else:
                height_data, coordinates = self._generate_sample_height_data()

            # 轉換為視覺化格式
            height_data_viz = []
            for i, (coord, height) in enumerate(zip(coordinates, height_data)):
                angle = np.arctan2(coord[1] - 150, coord[0] - 250)  # 計算角度
                height_data_viz.append({
                    'index': i,
                    'angle': angle,
                    'x': coord[0],
                    'y': coord[1],
                    'height': height,
                    'targetHeight': self.dispensing_spec.height
                })

            self.robot_path_data = height_data_viz
            self.update_visualization_data({
                'height_data': height_data_viz,
                'robot_path': height_data_viz,
                'current_position': 0
            })

            # 步驟2: 載入高度輪廓
            self.update_visualization("載入高度輪廓數據...", "info")
            success = self.dispensing_system.load_height_profile(height_data, coordinates)
            if not success:
                return {"error": "載入高度輪廓失敗"}

            # 步驟3: 處理激光掃描數據
            self.update_visualization("處理激光掃描數據...", "info")
            scan_params = {'scale_factor': 1.0, 'offset': 0.0}
            processed_data = self.height_detector.process_laser_scan_data(height_data, scan_params)

            # 步驟4: 表面分析
            self.update_visualization("分析表面拓撲...", "info")
            surface_analysis = self.surface_analyzer.analyze_surface_topology(processed_data, coordinates)

            # 步驟5: 計算補償
            self.update_visualization("計算補償映射...", "info")
            compensation_plan = self.compensation_calculator.calculate_compensation_map(
                surface_analysis,
                target_height=np.max(processed_data),
                edge_width=self.dispensing_spec.width
            )

            # 步驟6: 計算點膠點
            self.update_visualization("計算點膠點...", "info")
            success = self.dispensing_system.calculate_compensation_algorithm()
            if not success:
                return {"error": "補償算法計算失敗"}

            # 步驟7: 驗證表面平整度
            self.update_visualization("驗證表面平整度...", "info")
            flatness_metrics = self.dispensing_system.validate_surface_flatness()

            # 步驟8: 開始表面監控
            self.update_visualization("啟動表面監控...", "info")
            self.surface_monitor.start_monitoring()

            # 步驟9: 執行點膠（含PID控制和視覺化）
            self.update_visualization("開始邊緣塗膠作業...", "success")
            coating_success = self._execute_coating_with_visualization()

            # 步驟10: 品質檢測
            self.update_visualization("執行品質檢測...", "info")
            quality_measurements = self._perform_quality_inspection()

            # 步驟11: 生成品質報告
            quality_report = self.tolerance_controller.generate_quality_report(
                f"BLU_{int(time.time())}", quality_measurements
            )

            # 步驟12: 停止監控
            self.surface_monitor.stop_monitoring()

            # 步驟13: 獲取最終結果
            dispensing_plan = self.dispensing_system.get_dispensing_plan()
            coating_quality = self.coating_controller.get_quality_report()
            surface_status = self.surface_monitor.get_current_status()

            # 整合結果
            result = self._compile_results(surface_analysis, compensation_plan, dispensing_plan,
                                         flatness_metrics, coating_quality, quality_report, surface_status)

            self.update_visualization("BLU邊緣塗膠流程執行完成！", "success")
            logger.info("BLU邊緣塗膠流程執行完成")
            return result

        except Exception as e:
            logger.error(f"執行流程失敗: {e}")
            self.update_visualization(f"執行失敗: {e}", "error")
            return {"error": str(e)}

    def _execute_coating_with_visualization(self) -> bool:
        """執行塗膠過程（含PID控制和視覺化）"""
        path_points = [(point.x, point.y, point.z_target)
                      for point in self.dispensing_system.dispensing_points]
        compensation_data = [point.compensation_factor
                           for point in self.dispensing_system.dispensing_points]

        total_points = len(path_points)

        for i, ((x, y, z), _) in enumerate(zip(path_points, compensation_data)):
            # 計算原始高度誤差（補償前）
            current_height = self.robot_path_data[i]['height'] if i < len(self.robot_path_data) else 1.0
            target_height = self.dispensing_spec.height
            raw_height_error = target_height - current_height

            # 記錄補償前的原始誤差
            current_time = time.time() - self.start_time
            self.raw_error_history.append({
                'time': current_time,
                'raw_error': raw_height_error,
                'current_height': current_height,
                'target_height': target_height
            })

            # PID控制計算（這會產生補償後的誤差）
            pid_result = self.pid_controller.update(target_height, current_height)
            compensated_error = pid_result['error']  # PID處理後的誤差

            # 計算機器人速度（基於PID輸出）
            base_speed = 20.0  # mm/s
            speed_adjustment = abs(pid_result['output']) * 0.5
            robot_speed = max(5.0, base_speed - speed_adjustment)

            # 計算塗膠流量（三段式控制）- 使用補償後的誤差
            abs_error = abs(compensated_error)
            if abs_error > 0.15:
                flow_stage = 3
                flow_rate = 0.8  # cm³/s
            elif abs_error > 0.08:
                flow_stage = 2
                flow_rate = 0.6
            else:
                flow_stage = 1
                flow_rate = 0.4

            # 記錄PID數據（包含補償前後的誤差對比）
            self.pid_history.append({
                'time': current_time,
                'output': pid_result['output'],
                'error': compensated_error,  # 補償後誤差
                'raw_error': raw_height_error,  # 補償前原始誤差
                'proportional': pid_result['proportional'],
                'integral': pid_result['integral'],
                'derivative': pid_result['derivative']
            })

            self.flow_history.append({
                'time': current_time,
                'flow': flow_rate,
                'stage': flow_stage
            })

            # 更新視覺化
            progress = (i + 1) / total_points * 100
            self.update_visualization_data({
                'current_position': (x, y),
                'robot_speed': robot_speed,
                'flow_rate': flow_rate,
                'height_error': compensated_error,  # 補償後誤差
                'raw_error': raw_height_error,  # 補償前原始誤差
                'progress': progress,
                'pid_data': self.pid_history[-50:],  # 只保留最近50個數據點
                'flow_history': self.flow_history[-50:],
                'raw_error_history': self.raw_error_history[-50:],  # 原始誤差歷史
                'robot_path': self.robot_path_data,
                'current_position': i
            })

            # 模擬處理時間
            time.sleep(0.1)

            # 每10個點記錄一次日誌
            if (i + 1) % 10 == 0:
                self.update_visualization(
                    f"點膠進度: {i+1}/{total_points}, 原始誤差: {raw_height_error:.3f}mm, 補償後: {compensated_error:.3f}mm, 流量段: {flow_stage}",
                    "info"
                )

        return True

    def _perform_quality_inspection(self) -> List:
        """執行品質檢測"""
        quality_measurements = []

        # 模擬尺寸測量
        sample_image = np.random.randint(0, 255, (480, 640), dtype=np.uint8)
        dimension_results = self.tolerance_controller.measure_dimensions(
            sample_image, calibration_factor=100.0
        )
        quality_measurements.extend(dimension_results)

        # 表面平整度測量
        if self.robot_path_data:
            heights = [point['height'] for point in self.robot_path_data]
            flatness_result = self.tolerance_controller.evaluate_surface_flatness(np.array(heights))
            quality_measurements.append(flatness_result)

        return quality_measurements

    def _compile_results(self, surface_analysis, compensation_plan, dispensing_plan,
                        flatness_metrics, coating_quality, quality_report, surface_status) -> Dict:
        """編譯最終結果"""
        return {
            "success": True,
            "surface_analysis": {
                "mean_height": surface_analysis.mean_height,
                "height_range": surface_analysis.height_range,
                "roughness": surface_analysis.roughness,
                "critical_areas_count": len(surface_analysis.critical_areas)
            },
            "compensation_plan": {
                "total_volume_needed": compensation_plan.total_volume_needed,
                "max_compensation": compensation_plan.max_compensation,
                "quality_score": compensation_plan.quality_score
            },
            "dispensing_plan": dispensing_plan,
            "flatness_metrics": flatness_metrics,
            "coating_quality": coating_quality,
            "quality_report": {
                "overall_status": quality_report.overall_status.value,
                "pass_rate": quality_report.statistics.get("pass_rate", 0),
                "recommendations": quality_report.recommendations
            },
            "surface_monitoring": surface_status,
            "pid_performance": {
                "total_points": len(self.pid_history),
                "avg_error": np.mean([d['error'] for d in self.pid_history]) if self.pid_history else 0,
                "max_output": np.max([d['output'] for d in self.pid_history]) if self.pid_history else 0
            }
        }

    def update_visualization(self, message: str, level: str = "info"):
        """更新視覺化日誌"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] {level.upper()}: {message}")

    def update_visualization_data(self, data: Dict):
        """更新視覺化數據"""
        if self.enable_visualization and self.visualization_system:
            try:
                self.visualization_system.data_queue.put(data)
            except Exception as e:
                logger.error(f"更新視覺化數據失敗: {e}")

    def run_complete_process(self, height_data_file: str = None) -> Dict:
        """執行完整的點膠流程"""
        logger.info("開始執行完整的BLU邊緣塗膠流程")
        
        try:
            # 步驟1: 載入或生成高度數據
            if height_data_file:
                height_data, coordinates = self._load_height_data(height_data_file)
            else:
                height_data, coordinates = self._generate_sample_height_data()
            
            # 步驟2: 載入高度輪廓
            success = self.dispensing_system.load_height_profile(height_data, coordinates)
            if not success:
                return {"error": "載入高度輪廓失敗"}
            
            # 步驟3: 處理激光掃描數據
            scan_params = {'scale_factor': 1.0, 'offset': 0.0}
            processed_data = self.height_detector.process_laser_scan_data(height_data, scan_params)
            
            # 步驟4: 表面分析
            surface_analysis = self.surface_analyzer.analyze_surface_topology(processed_data, coordinates)
            
            # 步驟5: 計算補償
            compensation_plan = self.compensation_calculator.calculate_compensation_map(
                surface_analysis, 
                target_height=np.max(processed_data),
                edge_width=self.dispensing_spec.width
            )
            
            # 步驟6: 計算點膠點
            success = self.dispensing_system.calculate_compensation_algorithm()
            if not success:
                return {"error": "補償算法計算失敗"}
            
            # 步驟7: 驗證表面平整度
            flatness_metrics = self.dispensing_system.validate_surface_flatness()
            
            # 步驟8: 開始表面監控
            self.surface_monitor.start_monitoring()
            
            # 步驟9: 執行點膠
            path_points = [(point.x, point.y, point.z_target) 
                          for point in self.dispensing_system.dispensing_points]
            compensation_data = [point.compensation_factor 
                               for point in self.dispensing_system.dispensing_points]
            
            coating_success = self.coating_controller.execute_edge_coating(
                path_points, compensation_data
            )
            
            # 步驟10: 品質檢測
            quality_measurements = []
            
            # 模擬尺寸測量
            sample_image = np.random.randint(0, 255, (480, 640), dtype=np.uint8)
            dimension_results = self.tolerance_controller.measure_dimensions(
                sample_image, calibration_factor=100.0
            )
            quality_measurements.extend(dimension_results)
            
            # 表面平整度測量
            flatness_result = self.tolerance_controller.evaluate_surface_flatness(processed_data)
            quality_measurements.append(flatness_result)
            
            # 步驟11: 生成品質報告
            quality_report = self.tolerance_controller.generate_quality_report(
                f"BLU_{int(time.time())}", quality_measurements
            )
            
            # 步驟12: 停止監控
            self.surface_monitor.stop_monitoring()
            
            # 步驟13: 獲取最終結果
            dispensing_plan = self.dispensing_system.get_dispensing_plan()
            coating_quality = self.coating_controller.get_quality_report()
            surface_status = self.surface_monitor.get_current_status()
            
            # 整合結果
            result = {
                "success": coating_success,
                "surface_analysis": {
                    "mean_height": surface_analysis.mean_height,
                    "height_range": surface_analysis.height_range,
                    "roughness": surface_analysis.roughness,
                    "critical_areas_count": len(surface_analysis.critical_areas)
                },
                "compensation_plan": {
                    "total_volume_needed": compensation_plan.total_volume_needed,
                    "max_compensation": compensation_plan.max_compensation,
                    "quality_score": compensation_plan.quality_score
                },
                "dispensing_plan": dispensing_plan,
                "flatness_metrics": flatness_metrics,
                "coating_quality": coating_quality,
                "quality_report": {
                    "overall_status": quality_report.overall_status.value,
                    "pass_rate": quality_report.statistics.get("pass_rate", 0),
                    "recommendations": quality_report.recommendations
                },
                "surface_monitoring": surface_status
            }
            
            logger.info("BLU邊緣塗膠流程執行完成")
            return result
            
        except Exception as e:
            logger.error(f"執行流程失敗: {e}")
            return {"error": str(e)}
    
    def _load_height_data(self, file_path: str) -> Tuple[np.ndarray, np.ndarray]:
        """載入高度數據文件"""
        # 這裡應該實現實際的文件載入邏輯
        # 目前返回模擬數據
        return self._generate_sample_height_data()
    
    def _generate_sample_height_data(self) -> Tuple[np.ndarray, np.ndarray]:
        """生成樣本高度數據"""
        # 生成模擬的BLU底部高度數據
        x = np.linspace(0, 50, 100)  # 50mm長度
        y = np.linspace(0, 30, 60)   # 30mm寬度
        
        xx, yy = np.meshgrid(x, y)
        coordinates = np.column_stack([xx.ravel(), yy.ravel()])
        
        # 模擬底部起伏（中間較低，邊緣較高）
        center_x, center_y = 25, 15
        distance_from_center = np.sqrt((xx - center_x)**2 + (yy - center_y)**2)
        
        # 基礎高度 + 起伏
        base_height = 0.8  # mm
        variation = 0.3 * np.exp(-distance_from_center / 10)  # 中心下沉
        noise = np.random.normal(0, 0.02, xx.shape)  # 測量噪聲
        
        height_map = base_height + variation + noise
        
        logger.info(f"生成樣本數據 - 尺寸: {xx.shape}, 高度範圍: {np.min(height_map):.3f}-{np.max(height_map):.3f}mm")
        
        return height_map.ravel(), coordinates

def main():
    """主函數"""
    parser = argparse.ArgumentParser(description='BLU邊緣塗膠智能點膠系統')
    parser.add_argument('--height-data', type=str, help='高度數據文件路徑')
    parser.add_argument('--output', type=str, default='result.json', help='結果輸出文件')
    parser.add_argument('--visualization', action='store_true', help='啟用即時視覺化監控')
    parser.add_argument('--no-gui', action='store_true', help='禁用GUI，僅使用命令行模式')

    args = parser.parse_args()

    # 檢查是否啟用視覺化
    enable_viz = args.visualization and not args.no_gui

    if enable_viz:
        print("🚀 啟動BLU邊緣塗膠智能點膠系統（含即時視覺化）")
        print("=" * 60)
        print("功能特色:")
        print("• 即時PID控制監控")
        print("• 表面高度分佈視覺化")
        print("• 機器人路徑追蹤")
        print("• 三段式流量控制")
        print("• 品質指標即時顯示")
        print("=" * 60)
    else:
        print("🔧 啟動BLU邊緣塗膠智能點膠系統（命令行模式）")

    try:
        # 創建應用程式實例
        app = BLUDispensingApplication(enable_visualization=enable_viz)

        # 設置硬體回調
        app.setup_hardware_callbacks()

        # 執行完整流程
        if enable_viz:
            print("\n📊 正在啟動視覺化界面...")
            print("提示: 關閉視覺化窗口將結束程序")
            result = app.run_complete_process_with_visualization(args.height_data)
        else:
            result = app.run_complete_process(args.height_data)

        # 輸出結果
        import json

        # 轉換numpy bool為Python bool
        def convert_numpy_types(obj):
            if hasattr(obj, 'item'):
                return obj.item()
            elif isinstance(obj, dict):
                return {k: convert_numpy_types(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_numpy_types(v) for v in obj]
            else:
                return obj

        result_serializable = convert_numpy_types(result)

        with open(args.output, 'w', encoding='utf-8') as f:
            json.dump(result_serializable, f, indent=2, ensure_ascii=False)

        print(f"\n💾 執行完成，結果已保存至: {args.output}")

        # 顯示關鍵結果
        if result.get("success"):
            print("\n=== 📈 執行結果摘要 ===")
            print(f"🎯 表面分析 - 平均高度: {result['surface_analysis']['mean_height']:.3f}mm")
            print(f"⚙️  補償計劃 - 品質分數: {result['compensation_plan']['quality_score']:.2f}")
            print(f"📊 品質報告 - 通過率: {result['quality_report']['pass_rate']:.1%}")
            print(f"🔍 整體狀態: {result['quality_report']['overall_status']}")

            # 顯示PID性能（如果有視覺化數據）
            if 'pid_performance' in result:
                pid_perf = result['pid_performance']
                print(f"🎛️  PID性能 - 平均誤差: {pid_perf['avg_error']:.3f}mm")
                print(f"📈 PID性能 - 最大輸出: {pid_perf['max_output']:.3f}")
                print(f"📍 處理點數: {pid_perf['total_points']}")

            if result['quality_report']['recommendations']:
                print("\n💡 建議改善措施:")
                for i, rec in enumerate(result['quality_report']['recommendations'], 1):
                    print(f"   {i}. {rec}")

            print("\n✅ 系統運行成功！")
        else:
            print(f"\n❌ 執行失敗: {result.get('error', '未知錯誤')}")

    except KeyboardInterrupt:
        print("\n\n⏹️  用戶中斷執行")
    except Exception as e:
        print(f"\n❌ 系統錯誤: {e}")
        logger.error(f"主程序執行失敗: {e}")

    if enable_viz:
        print("\n👋 感謝使用BLU邊緣塗膠智能點膠系統！")

def run_visualization_demo():
    """運行視覺化演示"""
    print("🎮 啟動BLU邊緣塗膠PID控制演示模式")
    print("=" * 50)

    app = BLUDispensingApplication(enable_visualization=True)
    app.setup_hardware_callbacks()

    try:
        result = app.run_complete_process_with_visualization()
        print("演示完成！")
        return result
    except Exception as e:
        print(f"演示失敗: {e}")
        return None

if __name__ == "__main__":
    main()
