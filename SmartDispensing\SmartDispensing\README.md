# BLU邊緣塗膠智能點膠系統

## 專案概述

本系統專為BLU（背光單元）邊緣塗膠應用而設計，能夠處理BLU底部高低起伏的複雜情況，確保邊緣膠水塗佈的精確性和表面平整度。

### 核心功能

- **高度檢測與補償**: 自動檢測BLU底部的高低起伏，計算精確的膠水補償量
- **精確邊緣塗膠**: 實現寬度1.4mm、高度1.2mm、公差±0.2mm的精確塗膠控制
- **表面平整度監控**: 實時監控膠水表面平整度，避免塌陷影響玻璃貼合
- **品質控制**: 全面的公差控制和品質檢測系統
- **智能補償算法**: 基於表面分析的智能膠水補償計算

### 技術規格

| 參數 | 規格 | 備註 |
|------|------|------|
| 膠水寬度 | 1.4mm | 公差±0.2mm |
| 膠水高度 | 1.2mm | 公差±0.2mm |
| 表面平整度 | ≤0.05mm | 確保玻璃貼合品質 |
| 測量解析度 | 0.01mm | 高精度測量 |
| 監控頻率 | 10Hz | 實時監控 |

## 系統架構

```
BLU邊緣塗膠系統
├── 核心系統 (src/core/)
│   └── dispensing_system.py - 主要點膠系統
├── 算法模組 (src/algorithms/)
│   └── height_detection.py - 高度檢測與補償算法
├── 控制模組 (src/control/)
│   └── edge_coating_controller.py - 邊緣塗膠控制器
├── 監控模組 (src/monitoring/)
│   └── surface_monitor.py - 表面平整度監控
└── 品質模組 (src/quality/)
    └── tolerance_control.py - 公差控制與品質檢測
```

## 安裝與設置

### 環境要求

- Python 3.8+
- NumPy, OpenCV, SciPy等科學計算庫

### 安裝步驟

1. 克隆專案
```bash
git clone <repository-url>
cd SmartDispensing
```

2. 安裝依賴
```bash
pip install -r requirements.txt
```

3. 運行系統
```bash
python main.py
```

## 使用方法

### 基本使用

```python
from src.core.dispensing_system import BLUEdgeDispensingSystem, DispensingSpec

# 創建系統實例
spec = DispensingSpec(width=1.4, height=1.2, tolerance=0.2)
system = BLUEdgeDispensingSystem(spec)

# 載入高度數據
height_data = np.array([...])  # 您的高度測量數據
coordinates = np.array([...])  # 對應的座標數據
system.load_height_profile(height_data, coordinates)

# 計算補償
system.calculate_compensation_algorithm()

# 驗證表面平整度
flatness_metrics = system.validate_surface_flatness()
```

### 完整流程執行

```bash
# 使用預設參數執行
python main.py

# 指定高度數據文件
python main.py --height-data data/height_profile.csv

# 指定輸出文件
python main.py --output results/dispensing_result.json
```

## 核心模組說明

### 1. 高度檢測與補償算法

- **HeightDetectionSystem**: 處理激光掃描數據，進行噪聲濾波和校準
- **SurfaceAnalyzer**: 分析表面拓撲，識別關鍵補償區域
- **CompensationCalculator**: 計算精確的膠水補償量

### 2. 邊緣塗膠控制

- **EdgeCoatingController**: 精確控制點膠參數
- 自動調整流量、壓力、速度等參數
- 實時品質監控和反饋

### 3. 表面平整度監控

- **SurfaceMonitor**: 實時監控表面平整度
- 多級警報系統
- 自動品質評估和報告

### 4. 公差控制與品質檢測

- **ToleranceController**: 精確的尺寸測量和公差控制
- 製程能力分析
- 自動品質報告生成

## 品質保證

### 關鍵品質指標

1. **尺寸精度**: 寬度和高度控制在±0.2mm公差內
2. **表面平整度**: 確保≤0.05mm的表面平整度
3. **製程穩定性**: Cpk≥1.33的製程能力
4. **黏附品質**: 確保與玻璃的良好貼合

### 品質監控流程

1. 實時表面監控
2. 自動尺寸測量
3. 統計製程控制
4. 品質報告生成
5. 改善建議提供

## 故障排除

### 常見問題

1. **平整度不符合要求**
   - 檢查高度檢測數據品質
   - 調整補償算法參數
   - 驗證點膠路徑規劃

2. **尺寸偏差過大**
   - 校準測量系統
   - 調整點膠參數
   - 檢查硬體精度

3. **表面塌陷問題**
   - 增加膠水黏度
   - 調整固化條件
   - 優化補償策略

## 技術支援

### 日誌系統

系統提供詳細的日誌記錄，包括：
- 操作日誌: `blu_dispensing.log`
- 品質數據: 自動保存至結果文件
- 警報記錄: 實時警報和歷史記錄

### 性能優化

- 使用多線程處理提高效率
- 智能緩存減少重複計算
- 自適應參數調整

## 擴展功能

### 未來發展方向

1. **機器學習優化**: 基於歷史數據的智能參數優化
2. **3D視覺檢測**: 更精確的三維表面檢測
3. **預測性維護**: 設備狀態監控和預測
4. **雲端整合**: 遠程監控和數據分析

### API接口

系統提供標準API接口，支援：
- RESTful API
- 硬體驅動接口
- 數據庫整合
- 第三方系統整合

## 授權與版權

本專案採用MIT授權，詳見LICENSE文件。

## 聯絡資訊

如有技術問題或建議，請聯絡開發團隊。
