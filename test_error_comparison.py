#!/usr/bin/env python3
"""
測試補償前後誤差對比功能
"""

import sys
import os
import matplotlib.pyplot as plt
import numpy as np
import platform

# 添加src目錄到路徑
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# 設置中文字體
def setup_chinese_font():
    """設置matplotlib中文字體"""
    system = platform.system()
    
    if system == "Windows":
        chinese_fonts = ['Microsoft YaHei', 'SimHei', 'SimSun', 'KaiTi']
    elif system == "Darwin":  # macOS
        chinese_fonts = ['PingFang SC', 'Heiti SC', 'STHeiti', 'Arial Unicode MS']
    else:  # Linux
        chinese_fonts = ['WenQuanYi Micro Hei', 'DejaVu Sans', 'Liberation Sans']
    
    for font_name in chinese_fonts:
        try:
            plt.rcParams['font.sans-serif'] = [font_name]
            plt.rcParams['axes.unicode_minus'] = False
            print(f"✅ 成功設置字體: {font_name}")
            return font_name
        except:
            continue
    
    print("⚠️ 無法找到中文字體，使用默認字體")
    plt.rcParams['axes.unicode_minus'] = False
    return "默認字體"

class SimplePIDController:
    """簡化的PID控制器"""
    
    def __init__(self, kp=1.2, ki=0.1, kd=0.05):
        self.kp = kp
        self.ki = ki
        self.kd = kd
        self.previous_error = 0
        self.integral = 0
        self.last_time = 0
    
    def update(self, setpoint, current_value, dt=0.1):
        """更新PID控制器"""
        error = setpoint - current_value
        
        # PID計算
        proportional = self.kp * error
        self.integral += error * dt
        integral_term = self.ki * self.integral
        derivative = self.kd * (error - self.previous_error) / dt if dt > 0 else 0
        
        output = proportional + integral_term + derivative
        
        self.previous_error = error
        
        return {
            'output': output,
            'error': error,
            'proportional': proportional,
            'integral': integral_term,
            'derivative': derivative
        }

def test_error_comparison():
    """測試補償前後誤差對比"""
    print("🧪 測試BLU邊緣塗膠補償前後誤差對比...")
    
    # 設置字體
    font_name = setup_chinese_font()
    
    # 模擬參數
    scan_points = 100
    target_height = 1.2  # mm
    pid_controller = SimplePIDController()
    
    # 生成模擬BLU表面數據（有較大起伏）
    print("🔍 生成模擬BLU表面數據...")
    
    raw_errors = []
    compensated_errors = []
    pid_outputs = []
    times = []
    heights = []
    
    for i in range(scan_points):
        time_point = i * 0.1
        angle = (i / scan_points) * 2 * np.pi
        
        # 模擬表面高度變化（故意加大變化幅度）
        base_height = 1.0
        variation = 0.4 * np.sin(angle * 3) + 0.3 * np.cos(angle * 5) + 0.2 * np.sin(angle * 7)
        noise = (np.random.random() - 0.5) * 0.15
        current_height = max(0.3, min(1.8, base_height + variation + noise))
        
        # 計算原始誤差（補償前）
        raw_error = target_height - current_height
        
        # PID控制計算
        pid_result = pid_controller.update(target_height, current_height, 0.1)
        
        # 模擬補償效果（PID輸出會減少誤差）
        compensation_factor = 0.7  # 70%的補償效果
        compensated_error = raw_error * (1 - compensation_factor) + pid_result['output'] * 0.1
        
        # 記錄數據
        raw_errors.append(raw_error)
        compensated_errors.append(compensated_error)
        pid_outputs.append(pid_result['output'])
        times.append(time_point)
        heights.append(current_height)
    
    # 創建對比圖表
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle(f'BLU邊緣塗膠PID控制 - 補償前後誤差對比分析 (字體: {font_name})', fontsize=16, fontweight='bold')
    
    # 1. 補償前後誤差對比
    ax1 = axes[0, 0]
    ax1.plot(times, raw_errors, 'orange', linewidth=2.5, label='補償前誤差', alpha=0.8)
    ax1.plot(times, compensated_errors, 'r-', linewidth=2, label='補償後誤差')
    ax1.plot(times, pid_outputs, 'b-', linewidth=2, label='PID輸出')
    ax1.axhline(y=0, color='k', linestyle='--', alpha=0.5, label='目標線')
    
    # 填充改善區域
    improvement = [abs(raw) - abs(comp) for raw, comp in zip(raw_errors, compensated_errors)]
    positive_improvement = [max(0, imp) for imp in improvement]
    ax1.fill_between(times, 0, positive_improvement, alpha=0.2, color='green', label='改善效果')
    
    ax1.set_title('PID控制響應 (補償前後對比)', fontsize=12, pad=20)
    ax1.set_xlabel('時間 (s)', fontsize=10)
    ax1.set_ylabel('誤差/輸出值', fontsize=10)
    ax1.legend(fontsize=8, loc='upper right')
    ax1.grid(True, alpha=0.3)
    
    # 添加統計信息
    avg_raw_error = np.mean([abs(e) for e in raw_errors])
    avg_comp_error = np.mean([abs(e) for e in compensated_errors])
    improvement_rate = (avg_raw_error - avg_comp_error) / avg_raw_error * 100 if avg_raw_error > 0 else 0
    
    stats_text = f'平均改善: {improvement_rate:.1f}%\n原始誤差: {avg_raw_error:.3f}mm\n補償後: {avg_comp_error:.3f}mm'
    ax1.text(0.02, 0.98, stats_text, transform=ax1.transAxes, 
             verticalalignment='top', fontsize=8,
             bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    # 2. 表面高度分佈
    ax2 = axes[0, 1]
    scatter = ax2.scatter(times, heights, c=heights, cmap='coolwarm', s=30)
    ax2.axhline(y=target_height, color='red', linestyle='--', linewidth=2, label=f'目標高度 ({target_height}mm)')
    ax2.set_title('BLU表面高度分佈', fontsize=12, pad=20)
    ax2.set_xlabel('時間 (s)', fontsize=10)
    ax2.set_ylabel('高度 (mm)', fontsize=10)
    ax2.legend(fontsize=9)
    ax2.grid(True, alpha=0.3)
    
    try:
        cbar = fig.colorbar(scatter, ax=ax2, shrink=0.8)
        cbar.set_label('高度 (mm)', fontsize=10)
    except:
        pass
    
    # 3. 誤差分佈直方圖
    ax3 = axes[1, 0]
    ax3.hist([abs(e) for e in raw_errors], bins=20, alpha=0.7, color='orange', label='補償前誤差', density=True)
    ax3.hist([abs(e) for e in compensated_errors], bins=20, alpha=0.7, color='red', label='補償後誤差', density=True)
    ax3.set_title('誤差分佈直方圖', fontsize=12, pad=20)
    ax3.set_xlabel('絕對誤差 (mm)', fontsize=10)
    ax3.set_ylabel('密度', fontsize=10)
    ax3.legend(fontsize=9)
    ax3.grid(True, alpha=0.3)
    
    # 4. 改善效果統計
    ax4 = axes[1, 1]
    
    # 計算不同誤差範圍的改善效果
    error_ranges = ['<0.1mm', '0.1-0.2mm', '0.2-0.3mm', '>0.3mm']
    raw_counts = [
        sum(1 for e in raw_errors if abs(e) < 0.1),
        sum(1 for e in raw_errors if 0.1 <= abs(e) < 0.2),
        sum(1 for e in raw_errors if 0.2 <= abs(e) < 0.3),
        sum(1 for e in raw_errors if abs(e) >= 0.3)
    ]
    comp_counts = [
        sum(1 for e in compensated_errors if abs(e) < 0.1),
        sum(1 for e in compensated_errors if 0.1 <= abs(e) < 0.2),
        sum(1 for e in compensated_errors if 0.2 <= abs(e) < 0.3),
        sum(1 for e in compensated_errors if abs(e) >= 0.3)
    ]
    
    x = np.arange(len(error_ranges))
    width = 0.35
    
    ax4.bar(x - width/2, raw_counts, width, label='補償前', color='orange', alpha=0.7)
    ax4.bar(x + width/2, comp_counts, width, label='補償後', color='red', alpha=0.7)
    
    ax4.set_title('誤差範圍分佈統計', fontsize=12, pad=20)
    ax4.set_xlabel('誤差範圍', fontsize=10)
    ax4.set_ylabel('點數', fontsize=10)
    ax4.set_xticks(x)
    ax4.set_xticklabels(error_ranges)
    ax4.legend(fontsize=9)
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存圖表
    plt.savefig('error_comparison_analysis.png', dpi=300, bbox_inches='tight')
    print("📊 對比分析圖表已保存為 'error_comparison_analysis.png'")
    
    # 顯示詳細統計結果
    print("\n📊 補償前後誤差對比統計:")
    print("=" * 50)
    print(f"📈 補償前統計:")
    print(f"   • 平均絕對誤差: {avg_raw_error:.3f}mm")
    print(f"   • 最大絕對誤差: {max([abs(e) for e in raw_errors]):.3f}mm")
    print(f"   • 標準差: {np.std([abs(e) for e in raw_errors]):.3f}mm")
    
    print(f"\n📉 補償後統計:")
    print(f"   • 平均絕對誤差: {avg_comp_error:.3f}mm")
    print(f"   • 最大絕對誤差: {max([abs(e) for e in compensated_errors]):.3f}mm")
    print(f"   • 標準差: {np.std([abs(e) for e in compensated_errors]):.3f}mm")
    
    print(f"\n✅ 改善效果:")
    print(f"   • 平均誤差改善: {improvement_rate:.1f}%")
    print(f"   • 最大誤差改善: {(max([abs(e) for e in raw_errors]) - max([abs(e) for e in compensated_errors])):.3f}mm")
    
    # 品質評估
    tolerance = 0.2  # ±0.2mm公差
    raw_within_tolerance = sum(1 for e in raw_errors if abs(e) <= tolerance)
    comp_within_tolerance = sum(1 for e in compensated_errors if abs(e) <= tolerance)
    
    print(f"\n🎯 品質評估 (公差±{tolerance}mm):")
    print(f"   • 補償前合格率: {raw_within_tolerance/len(raw_errors)*100:.1f}% ({raw_within_tolerance}/{len(raw_errors)})")
    print(f"   • 補償後合格率: {comp_within_tolerance/len(compensated_errors)*100:.1f}% ({comp_within_tolerance}/{len(compensated_errors)})")
    print(f"   • 合格率提升: {(comp_within_tolerance-raw_within_tolerance)/len(raw_errors)*100:.1f}%")
    
    print("=" * 50)
    
    # 顯示圖表
    plt.show()
    
    return True

def main():
    """主函數"""
    print("🧪 BLU邊緣塗膠系統 - 補償前後誤差對比測試")
    print("=" * 60)
    
    try:
        success = test_error_comparison()
        if success:
            print("\n✅ 補償前後誤差對比測試完成！")
            print("📋 測試結果:")
            print("   ✅ 補償前誤差趨勢線顯示正常")
            print("   ✅ 補償後誤差趨勢線顯示正常")
            print("   ✅ 改善效果區域填充正常")
            print("   ✅ 統計信息顯示正常")
            print("   ✅ 對比分析圖表生成成功")
        else:
            print("\n❌ 測試失敗")
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
