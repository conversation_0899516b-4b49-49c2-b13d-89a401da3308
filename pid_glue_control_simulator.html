<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PID塗膠控制系統模擬器</title>
    <style>
        body {
            font-family: 'Microsoft JhengHei', <PERSON>l, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50, #3498db);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: bold;
        }

        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 16px;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 20px;
            padding: 20px;
        }

        .charts-container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .chart-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border: 2px solid #e9ecef;
        }

        .control-panel {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border: 2px solid #e9ecef;
        }

        .status-display {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }

        .status-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 2px solid #dee2e6;
        }

        .status-label {
            font-size: 14px;
            color: #6c757d;
            margin-bottom: 5px;
        }

        .status-value {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
        }

        .target-value {
            color: #e74c3c;
        }

        .current-value {
            color: #27ae60;
        }

        .control-group {
            margin-bottom: 20px;
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 2px solid #dee2e6;
        }

        .control-group h3 {
            margin: 0 0 15px 0;
            color: #2c3e50;
            font-size: 16px;
            border-bottom: 2px solid #3498db;
            padding-bottom: 5px;
        }

        .parameter-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .parameter-label {
            font-weight: bold;
            color: #495057;
            min-width: 120px;
        }

        .parameter-input {
            flex: 1;
            margin: 0 10px;
        }

        .parameter-value {
            min-width: 60px;
            text-align: right;
            font-weight: bold;
            color: #2c3e50;
        }

        input[type="range"] {
            width: 100%;
            height: 6px;
            border-radius: 3px;
            background: #ddd;
            outline: none;
            -webkit-appearance: none;
        }

        input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #3498db;
            cursor: pointer;
            border: 2px solid white;
            box-shadow: 0 2px 6px rgba(0,0,0,0.3);
        }

        .button-group {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 15px;
        }

        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
            text-align: center;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
            transform: translateY(-2px);
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn-success:hover {
            background: #229954;
            transform: translateY(-2px);
        }

        .btn-warning {
            background: #f39c12;
            color: white;
        }

        .btn-warning:hover {
            background: #e67e22;
            transform: translateY(-2px);
        }

        #chart {
            width: 100%;
            height: 400px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            background: white;
            cursor: crosshair;
        }

        .instructions {
            background: #e8f4fd;
            border: 2px solid #3498db;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
        }

        .instructions h4 {
            margin: 0 0 10px 0;
            color: #2c3e50;
        }

        .instructions ol {
            margin: 0;
            padding-left: 20px;
        }

        .instructions li {
            margin-bottom: 5px;
            color: #34495e;
        }

        .system-info {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
            font-size: 14px;
            color: #495057;
        }

        .error-display {
            background: white;
            padding: 10px;
            border-radius: 6px;
            margin-bottom: 10px;
            border: 2px solid #dee2e6;
        }

        .error-label {
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 3px;
        }

        .error-value {
            font-size: 16px;
            font-weight: bold;
        }

        .error-proportional { color: #e74c3c; }
        .error-integral { color: #f39c12; }
        .error-derivative { color: #9b59b6; }
        .error-total { color: #2c3e50; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 PID塗膠控制系統模擬器</h1>
            <p>模擬不同PID參數對塗膠高度控制的影響 - 即時視覺化分析工具</p>
        </div>

        <div class="main-content">
            <div class="charts-container">
                <div class="chart-section">
                    <h3 style="margin: 0 0 15px 0; color: #2c3e50;">🌊 表面高度分佈圖</h3>
                    <div style="margin-bottom: 10px; padding: 8px; background: #e8f4fd; border-radius: 5px; font-size: 11px; color: #2c3e50; display: flex; justify-content: space-between; align-items: center;">
                        <div>
                            <strong>📊 圖表說明：</strong>
                            Y軸顯示絕對高度 (mm) | 目標：1.2mm (紅線) | 公差：1.15-1.25mm (綠區) | 🖱️ 滑鼠框選可縮放
                        </div>
                        <div style="display: flex; gap: 5px;">
                            <button class="btn btn-secondary" id="reset-zoom" style="padding: 4px 8px; font-size: 11px; display: none;">🔍 重置縮放</button>
                            <button class="btn btn-primary" id="generate-surface" style="padding: 4px 8px; font-size: 11px;">🔄 生成表面數據</button>
                        </div>
                    </div>
                    <div id="surface-legend" style="margin-bottom: 10px; padding: 8px; background: #f8f9fa; border-radius: 5px; border: 1px solid #dee2e6;">
                        <!-- 圖例將在這裡動態生成 -->
                    </div>
                    <canvas id="surface-chart"></canvas>
                    <div style="margin-top: 8px; display: flex; justify-content: space-between; align-items: center;">
                        <span style="font-size: 12px; color: #6c757d;">
                            已生成 <span id="point-count">0</span> 個數據點
                        </span>
                        <label style="font-size: 12px; color: #6c757d; display: flex; align-items: center; gap: 5px;">
                            <input type="checkbox" id="auto-scroll-checkbox" checked>
                            自動滾動到當前點
                        </label>
                    </div>
                </div>

                <div class="chart-section">
                    <h3 style="margin: 0 0 15px 0; color: #2c3e50;">� 表面高度數據表</h3>
                    <p style="margin: 0 0 10px 0; font-size: 12px; color: #6c757d;">
                        💡 PID參數說明：每次模擬使用固定的PID參數組合，所有點的Kp、Ki、Kd相同。智能點膠會自動尋找最佳參數組合。
                    </p>
                    <div style="max-height: 400px; overflow-y: auto; border: 2px solid #dee2e6; border-radius: 8px; background: white;">
                        <table id="surface-data-table" style="width: 100%; border-collapse: collapse; font-size: 12px;">
                            <thead style="background: #f8f9fa; position: sticky; top: 0;">
                                <tr>
                                    <th style="padding: 6px; border: 1px solid #dee2e6; text-align: center; font-size: 11px;">POINT NO</th>
                                    <th style="padding: 6px; border: 1px solid #dee2e6; text-align: center; font-size: 11px;">X座標 (mm)</th>
                                    <th style="padding: 6px; border: 1px solid #dee2e6; text-align: center; font-size: 11px;">Y座標 (mm)</th>
                                    <th style="padding: 6px; border: 1px solid #dee2e6; text-align: center; font-size: 11px;">補償前高度 (mm)</th>
                                    <th style="padding: 6px; border: 1px solid #dee2e6; text-align: center; font-size: 11px;">補償後高度 (mm)</th>
                                    <th style="padding: 6px; border: 1px solid #dee2e6; text-align: center; font-size: 11px;">流量 (cm³/s)</th>
                                    <th style="padding: 6px; border: 1px solid #dee2e6; text-align: center; font-size: 11px;">速度 (mm/s)</th>
                                    <th style="padding: 6px; border: 1px solid #dee2e6; text-align: center; font-size: 11px;">Kp</th>
                                    <th style="padding: 6px; border: 1px solid #dee2e6; text-align: center; font-size: 11px;">Ki</th>
                                    <th style="padding: 6px; border: 1px solid #dee2e6; text-align: center; font-size: 11px;">Kd</th>
                                    <th style="padding: 6px; border: 1px solid #dee2e6; text-align: center; font-size: 11px;">品質狀態</th>
                                </tr>
                            </thead>
                            <tbody id="surface-data-tbody">
                                <tr>
                                    <td colspan="11" style="padding: 20px; text-align: center; color: #6c757d;">
                                        請先生成表面數據
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="instructions" style="margin-top: 15px;">
                        <h4>💡 操作說明：</h4>
                        <ol>
                            <li>點擊"生成表面數據"獲取100點波浪表面</li>
                            <li>調整PID參數優化控制效果</li>
                            <li>設定移動速度和流量控制模式</li>
                            <li>開始模擬觀察塗膠效果和數據變化</li>
                        </ol>
                    </div>
                </div>
            </div>

            <div class="control-panel">
                <div class="status-display">
                    <div class="status-item">
                        <div class="status-label">目標高度</div>
                        <div class="status-value target-value" id="target-display">1.20mm</div>
                    </div>
                    <div class="status-item">
                        <div class="status-label">當前高度</div>
                        <div class="status-value current-value" id="current-display">0.80mm</div>
                    </div>
                    <div class="status-item">
                        <div class="status-label">總時間</div>
                        <div class="status-value" id="total-time-display">0.0秒</div>
                    </div>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; padding: 10px; background: #f8f9fa; border-radius: 5px; border: 1px solid #dee2e6;">
                        <div class="status-item">
                            <div class="status-label" style="font-size: 11px;">掃描時間</div>
                            <div class="status-value" id="scan-time-display">20.0秒</div>
                        </div>
                        <div class="status-item">
                            <div class="status-label" style="font-size: 11px;">設定</div>
                            <div class="status-value">
                                <input type="number" id="scan-time-setting" min="5" max="60" step="1" value="20"
                                       style="width: 45px; font-size: 11px; padding: 2px; border: 1px solid #ccc; border-radius: 3px;">秒
                            </div>
                        </div>
                    </div>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; padding: 10px; background: #f8f9fa; border-radius: 5px; border: 1px solid #dee2e6;">
                        <div class="status-item">
                            <div class="status-label" style="font-size: 11px;">計算時間</div>
                            <div class="status-value" id="calc-time-display">0.0秒</div>
                        </div>
                        <div class="status-item">
                            <div class="status-label" style="font-size: 11px;">塗膠時間</div>
                            <div class="status-value" id="dispensing-time-display">0.0秒</div>
                        </div>
                    </div>
                </div>

                <div class="control-group">
                    <h3>🎛️ PID控制參數</h3>
                    <div class="parameter-row">
                        <span class="parameter-label">比例增益 (P)</span>
                        <input type="range" id="kp-slider" class="parameter-input" min="0" max="10" step="0.1" value="2.0">
                        <span class="parameter-value" id="kp-value">2.0</span>
                    </div>
                    <div class="parameter-row">
                        <span class="parameter-label">積分增益 (I)</span>
                        <input type="range" id="ki-slider" class="parameter-input" min="0" max="2" step="0.01" value="0.00">
                        <span class="parameter-value" id="ki-value">0.00</span>
                    </div>
                    <div class="parameter-row">
                        <span class="parameter-label">微分增益 (D)</span>
                        <input type="range" id="kd-slider" class="parameter-input" min="0" max="5" step="0.01" value="0.00">
                        <span class="parameter-value" id="kd-value">0.00</span>
                    </div>
                </div>

                <div class="button-group">
                    <button class="btn btn-primary" id="start-btn">▶️ 開始模擬</button>
                    <button class="btn btn-success" id="smart-dispensing-btn">🧠 智能點膠</button>
                    <button class="btn btn-secondary" id="clear-smart-btn" style="display: none;">🔄 清除智能結果</button>
                </div>

                <div style="display: flex; gap: 10px; align-items: center; margin: 10px 0; padding: 8px; background: #f8f9fa; border-radius: 5px; border: 1px solid #dee2e6;">
                    <label style="display: flex; align-items: center; gap: 5px; font-size: 12px; color: #495057; cursor: pointer; margin: 0;">
                        <input type="checkbox" id="enable-animation" checked style="margin: 0;">
                        <span>🎬 啟用動畫效果</span>
                    </label>
                    <small style="color: #6c757d; font-size: 11px;">關閉可快速顯示智能點膠結果</small>
                </div>

                <div class="button-group">
                    <button class="btn btn-success" id="quick-10s">⚡ 快速模擬10秒</button>
                    <button class="btn btn-warning" id="quick-30s">⚡ 快速模擬30秒</button>
                    <button class="btn btn-info" id="speed-test">🚀 速度影響測試</button>
                    <button class="btn btn-outline-primary" id="dispensing-test">🧪 塗膠測試</button>
                </div>

                <div class="control-group">
                    <h3>🌊 表面參數設定</h3>
                    <div class="parameter-row">
                        <span class="parameter-label">波浪幅度</span>
                        <input type="range" id="wave-amplitude" class="parameter-input" min="0.05" max="0.5" step="0.05" value="0.2">
                        <span class="parameter-value" id="wave-amplitude-value">0.2mm</span>
                    </div>
                    <div class="parameter-row">
                        <span class="parameter-label">波浪頻率</span>
                        <input type="range" id="wave-frequency" class="parameter-input" min="1" max="10" step="1" value="3">
                        <span class="parameter-value" id="wave-frequency-value">3</span>
                    </div>
                    <div class="parameter-row">
                        <span class="parameter-label">隨機噪聲</span>
                        <input type="range" id="surface-noise" class="parameter-input" min="0" max="0.1" step="0.01" value="0.05">
                        <span class="parameter-value" id="surface-noise-value">0.05mm</span>
                    </div>
                </div>

                <div class="control-group">
                    <h3>🚀 移動控制參數</h3>
                    <div class="parameter-row">
                        <span class="parameter-label">基礎速度</span>
                        <input type="range" id="base-speed" class="parameter-input" min="5" max="100" step="0.1" value="20">
                        <span class="parameter-value" id="base-speed-value">20.00mm/s</span>
                    </div>
                    <div class="parameter-row">
                        <span class="parameter-label">速度調整範圍</span>
                        <input type="range" id="speed-range" class="parameter-input" min="5" max="95" step="0.1" value="50">
                        <span class="parameter-value" id="speed-range-value">±50.00%</span>
                    </div>
                    <div class="parameter-row">
                        <span class="parameter-label">目標公差</span>
                        <input type="range" id="target-tolerance" class="parameter-input" min="0.01" max="0.2" step="0.01" value="0.05">
                        <span class="parameter-value" id="target-tolerance-value">±0.05mm</span>
                    </div>
                </div>

                <div class="control-group">
                    <h3>💧 三段流量控制</h3>
                    <div class="parameter-row">
                        <span class="parameter-label">流量控制</span>
                        <select id="flow-control-mode" style="flex: 1; margin: 0 10px; padding: 5px;">
                            <option value="auto">自動調整</option>
                            <option value="manual">手動設定</option>
                            <option value="off">關閉</option>
                        </select>
                    </div>
                    <div class="parameter-row">
                        <span class="parameter-label">大流量</span>
                        <input type="range" id="flow-high" class="parameter-input" min="0.5" max="2.0" step="0.1" value="1.5">
                        <span class="parameter-value" id="flow-high-value">1.5cm³/s</span>
                    </div>
                    <div class="parameter-row">
                        <span class="parameter-label">中流量</span>
                        <input type="range" id="flow-medium" class="parameter-input" min="0.3" max="1.5" step="0.1" value="1.0">
                        <span class="parameter-value" id="flow-medium-value">1.0cm³/s</span>
                    </div>
                    <div class="parameter-row">
                        <span class="parameter-label">小流量</span>
                        <input type="range" id="flow-low" class="parameter-input" min="0.1" max="1.0" step="0.1" value="0.5">
                        <span class="parameter-value" id="flow-low-value">0.5cm³/s</span>
                    </div>
                    <div class="parameter-row" id="manual-flow-selection" style="display: none;">
                        <span class="parameter-label">手動選擇</span>
                        <div style="flex: 1; margin: 0 10px; display: flex; gap: 10px;">
                            <label style="display: flex; align-items: center; gap: 5px; font-size: 12px;">
                                <input type="radio" name="manual-flow" value="high" id="manual-flow-high">
                                大流量
                            </label>
                            <label style="display: flex; align-items: center; gap: 5px; font-size: 12px;">
                                <input type="radio" name="manual-flow" value="medium" id="manual-flow-medium" checked>
                                中流量
                            </label>
                            <label style="display: flex; align-items: center; gap: 5px; font-size: 12px;">
                                <input type="radio" name="manual-flow" value="low" id="manual-flow-low">
                                小流量
                            </label>
                        </div>
                    </div>
                </div>

                <div class="parameter-group">
                    <h4 style="margin: 0 0 10px 0; color: #2c3e50;">🧠 智能點膠設定</h4>
                    <div class="parameter-row">
                        <span class="parameter-label">迭代次數</span>
                        <input type="range" id="smart-iterations" class="parameter-input" min="5" max="50" step="1" value="20">
                        <span class="parameter-value" id="smart-iterations-value">20次</span>
                    </div>
                    <div class="parameter-row">
                        <span class="parameter-label">目標達標率</span>
                        <input type="range" id="target-pass-rate" class="parameter-input" min="80" max="98" step="1" value="90">
                        <span class="parameter-value" id="target-pass-rate-value">90%</span>
                    </div>
                    <div class="parameter-row">
                        <span class="parameter-label">優化模式</span>
                        <div style="flex: 1; margin: 0 10px; display: flex; gap: 10px; align-items: center;">
                            <label style="display: flex; align-items: center; gap: 5px; font-size: 12px;">
                                <input type="radio" name="optimization-mode" value="intelligent" id="mode-intelligent" checked>
                                🧠 智能算法
                            </label>
                            <label style="display: flex; align-items: center; gap: 5px; font-size: 12px;">
                                <input type="radio" name="optimization-mode" value="pid" id="mode-pid">
                                🔄 PID優化
                            </label>
                        </div>
                    </div>
                </div>

                <div class="parameter-group">
                    <h4 style="margin: 0 0 10px 0; color: #2c3e50;">⚙️ 系統性能設定</h4>
                    <div class="parameter-row">
                        <span class="parameter-label">系統負載</span>
                        <input type="range" id="system-load" class="parameter-input" min="0" max="40" step="1" value="10">
                        <span class="parameter-value" id="system-load-value">10</span>
                    </div>
                    <div class="parameter-row">
                        <span class="parameter-label">系統延遲</span>
                        <input type="range" id="system-delay" class="parameter-input" min="0" max="5" step="0.1" value="2">
                        <span class="parameter-value" id="system-delay-value">2.0秒</span>
                    </div>
                    <div class="parameter-row">
                        <span class="parameter-label">結果重現</span>
                        <div style="flex: 1; margin: 0 10px; display: flex; gap: 10px; align-items: center;">
                            <label style="display: flex; align-items: center; gap: 5px; font-size: 12px;">
                                <input type="checkbox" id="use-fixed-seed" checked>
                                🔒 固定種子 (確保結果一致)
                            </label>
                        </div>
                    </div>
                </div>

                <div class="control-group">
                    <h3>📊 即時狀態監控</h3>
                    <div class="error-display">
                        <div class="error-label">當前位置</div>
                        <div class="error-value" style="color: #3498db;" id="current-position">0/100</div>
                    </div>
                    <div class="error-display">
                        <div class="error-label">移動速度</div>
                        <div class="error-value" style="color: #e67e22;" id="current-speed">0mm/s</div>
                    </div>
                    <div class="error-display">
                        <div class="error-label">速度調整</div>
                        <div class="error-value" style="color: #f39c12;" id="speed-adjustment">100%</div>
                    </div>
                    <div class="error-display">
                        <div class="error-label">速度範圍</div>
                        <div class="error-value" style="color: #8e44ad;" id="speed-range-display">10-30mm/s</div>
                    </div>
                    <div class="error-display">
                        <div class="error-label">速度補償</div>
                        <div class="error-value" style="color: #2ecc71;" id="speed-compensation">0.000mm</div>
                    </div>
                    <div class="error-display">
                        <div class="error-label">智能狀態</div>
                        <div class="error-value" style="color: #9b59b6;" id="smart-status">待機</div>
                    </div>
                    <div class="error-display">
                        <div class="error-label">當前達標率</div>
                        <div class="error-value" style="color: #27ae60;" id="current-pass-rate">0%</div>
                    </div>
                    <div class="error-display">
                        <div class="error-label">系統負載</div>
                        <div class="error-value" style="color: #f39c12;" id="current-system-load">10</div>
                    </div>
                    <div class="error-display">
                        <div class="error-label">系統延遲</div>
                        <div class="error-value" style="color: #e67e22;" id="current-system-delay">2.0s</div>
                    </div>
                    <div class="error-display">
                        <div class="error-label">流量段位</div>
                        <div class="error-value" style="color: #9b59b6;" id="current-flow">待機</div>
                    </div>
                    <div class="error-display">
                        <div class="error-label">流量補償</div>
                        <div class="error-value" style="color: #16a085;" id="flow-compensation">0.000mm</div>
                    </div>
                    <div class="error-display">
                        <div class="error-label">流量效果</div>
                        <div class="error-value" style="color: #8e44ad;" id="flow-effect-ratio">0%</div>
                    </div>
                    <div class="error-display">
                        <div class="error-label">高度誤差</div>
                        <div class="error-value error-total" id="height-error">0.000mm</div>
                    </div>
                </div>

                <div class="control-group">
                    <h3>📈 品質統計</h3>
                    <div class="error-display">
                        <div class="error-label">合格點數</div>
                        <div class="error-value" style="color: #27ae60;" id="quality-pass">0</div>
                    </div>
                    <div class="error-display">
                        <div class="error-label">超差點數</div>
                        <div class="error-value" style="color: #e74c3c;" id="quality-fail">0</div>
                    </div>
                    <div class="error-display">
                        <div class="error-label">合格率</div>
                        <div class="error-value" style="color: #2c3e50;" id="quality-rate">0%</div>
                    </div>
                    <div class="error-display">
                        <div class="error-label">平均誤差</div>
                        <div class="error-value" style="color: #34495e;" id="avg-error">0.000mm</div>
                    </div>
                </div>

                <div class="system-info">
                    <h4 style="margin: 0 0 10px 0; color: #2c3e50;">🔬 系統說明：</h4>
                    <p><strong>模擬場景：</strong> 智能塗膠機器人表面追蹤系統</p>
                    <p><strong>控制目標：</strong> 維持目標高度 1.2mm ±0.05mm</p>
                    <p><strong>表面特性：</strong> 100點波浪表面，幅度±0.2mm</p>
                    <p><strong>控制策略：</strong> PID控制移動速度+三段流量調整</p>
                    <p><strong>品質標準：</strong> 綠色=合格，紅色=超差</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 智能點膠趨勢圖模態框 -->
    <div id="smart-trend-modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.8); z-index: 1000;">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 90%; max-width: 1200px; height: 80%; background: white; border-radius: 10px; padding: 20px; overflow-y: auto;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h2 style="margin: 0; color: #2c3e50;">🧠 智能點膠優化趨勢圖</h2>
                <button onclick="closeSmartTrendModal()" style="background: #e74c3c; color: white; border: none; padding: 10px 15px; border-radius: 5px; cursor: pointer;">✕ 關閉</button>
            </div>
            <canvas id="smart-trend-chart" width="1100" height="600" style="border: 1px solid #dee2e6; border-radius: 5px;"></canvas>
            <div id="smart-trend-summary" style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 5px;">
                <!-- 趨勢總結將在這裡顯示 -->
            </div>
        </div>
    </div>

    <script>
        // 全局變數
        let surfaceCanvas, surfaceCtx;
        let isRunning = false;
        let animationId = null;
        let startTime = null;

        // 智能點膠控制
        let isSmartDispensing = false;
        let smartIterationCount = 0;
        let maxSmartIterations = 20; // 可調整的最大迭代次數
        let bestParams = {
            kp: 2.0, ki: 0.1, kd: 0.05,
            baseSpeed: 20, speedRange: 50,
            flowHigh: 1.5, flowMedium: 1.0, flowLow: 0.5
        };
        let bestPerformance = 0; // 改為達標率，越高越好
        let optimizationHistory = [];
        let targetPassRate = 0.9; // 可調整的目標達標率
        let smartDispensingCompleted = false; // 標記智能點膠是否已完成
        let smartOptimizedData = null; // 保存智能算法優化的數據
        let enableAnimation = true; // 動畫開關

        // 時間統計相關變數
        let scanTime = 20.0; // 掃描表面時間（可設定，預設20秒）
        let calcTime = 0.0; // 計算時間（智能點膠純計算耗費時間）
        let dispensingTime = 0.0; // 塗膠時間
        let totalTime = 0.0; // 總時間
        let calcStartTime = 0; // 計算開始時間
        let dispensingStartTime = 0; // 塗膠開始時間
        let isInSmartCalculation = false; // 是否在智能點膠計算階段

        // 智能優化算法相關
        let useIntelligentAlgorithm = true; // 是否使用智能算法
        let pointCompensationMap = new Map(); // 每個點的最佳補償量

        // 系統性能參數
        let systemLoad = 10; // 系統負載 0-40
        let systemDelay = 2.0; // 系統延遲 0-5秒

        // 隨機種子系統 - 確保結果可重現
        let randomSeed = 12345;
        let useFixedSeed = true; // 是否使用固定種子

        // 簡單的偽隨機數生成器 (線性同餘生成器)
        function seededRandom() {
            if (useFixedSeed) {
                randomSeed = (randomSeed * 9301 + 49297) % 233280;
                return randomSeed / 233280;
            } else {
                return Math.random();
            }
        }

        // 重置隨機種子
        function resetRandomSeed() {
            randomSeed = 12345;
        }

        // PID控制器變數
        let kp = 2.0, ki = 0.0, kd = 0.0;
        let targetHeight = 1.2; // 目標塗膠高度
        let currentHeight = 1.0; // 當前高度(從基準表面開始)
        let lastError = 0;
        let integral = 0;

        // 表面數據
        let surfaceData = [];
        let currentPointIndex = 0;
        let totalPoints = 100;

        // 圖表縮放相關變數
        let isZoomed = false;
        let zoomRange = { startIndex: 0, endIndex: 99 }; // 預設顯示全部100個點
        let isSelecting = false;
        let selectionStart = { x: 0, y: 0 };
        let selectionEnd = { x: 0, y: 0 };

        // 移動控制參數
        let baseSpeed = 20; // mm/s
        let speedRange = 50; // ±50%
        let targetTolerance = 0.05; // ±0.05mm

        // 流量控制參數
        let flowControlMode = 'auto';
        let flowHigh = 1.5, flowMedium = 1.0, flowLow = 0.5;
        let manualFlowSelection = 'medium'; // high, medium, low (手動模式時使用)
        let currentFlow = 0;
        let currentSpeed = 0;

        // 表面生成參數
        let waveAmplitude = 0.2;
        let waveFrequency = 3;
        let surfaceNoise = 0.05;

        // 品質統計
        let qualityPass = 0;
        let qualityFail = 0;
        let totalErrors = [];

        // 數據記錄 (保留用於其他用途)
        let timeData = [];
        let heightData = [];
        let targetData = [];
        let speedData = [];
        let flowData = [];

        // 表格更新控制
        let lastTableUpdateIndex = -1;
        
        // 圖表參數
        const chartWidth = 700;
        const chartHeight = 300;
        const padding = 60;
        const timeWindow = 30; // 顯示30秒的數據
        
        // 全局錯誤處理
        window.addEventListener('error', function(event) {
            console.error('Global error:', event.error);
            if (isRunning) {
                stopSimulation();
            }
        });

        window.addEventListener('unhandledrejection', function(event) {
            console.error('Unhandled promise rejection:', event.reason);
        });

        // 安全的初始化函數
        function safeInitialize() {
            try {
                console.log('開始初始化系統...');

                // 載入保存的參數
                if (loadParameters()) {
                    console.log('已載入保存的參數設定');
                }

                // 檢查必要的DOM元素
                const requiredElements = ['surface-chart', 'surface-data-table', 'start-btn'];
                const missingElements = [];

                requiredElements.forEach(id => {
                    if (!document.getElementById(id)) {
                        missingElements.push(id);
                    }
                });

                if (missingElements.length > 0) {
                    throw new Error('缺少必要的DOM元素: ' + missingElements.join(', '));
                }

                // 初始化Canvas
                if (!initializeCanvas()) {
                    throw new Error('Canvas初始化失敗');
                }

                // 設置事件監聽器
                setupEventListeners();

                // 更新UI為載入的參數值
                updateUIFromParameters();

                // 生成初始數據
                generateSurfaceData();

                // 初始化UI組件
                updateSurfaceLegend();
                updateSurfaceDataTable();
                updateDisplay();

                // 繪製初始圖表
                if (!drawSurfaceChart()) {
                    throw new Error('初始圖表繪製失敗');
                }

                console.log('PID塗膠控制模擬器初始化成功');
                console.log('表面數據點數:', surfaceData ? surfaceData.length : 0);

                return true;

            } catch (error) {
                console.error('初始化錯誤:', error);
                return false;
            }
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM載入完成，開始初始化...');

            // 延遲初始化確保DOM完全準備好
            setTimeout(() => {
                if (!safeInitialize()) {
                    console.warn('首次初始化失敗，嘗試重試...');

                    // 重試一次
                    setTimeout(() => {
                        if (!safeInitialize()) {
                            alert('系統初始化失敗，請檢查瀏覽器控制台錯誤信息並重新載入頁面');
                        }
                    }, 500);
                }
            }, 100);
        });

        function initializeCanvas() {
            try {
                surfaceCanvas = document.getElementById('surface-chart');
                if (!surfaceCanvas) {
                    throw new Error('找不到surface-chart元素');
                }

                surfaceCtx = surfaceCanvas.getContext('2d');
                if (!surfaceCtx) {
                    throw new Error('無法獲取Canvas 2D context');
                }

                surfaceCanvas.width = chartWidth;
                surfaceCanvas.height = chartHeight;

                console.log('Canvas初始化成功:', surfaceCanvas.width + 'x' + surfaceCanvas.height);
                return true;
            } catch (error) {
                console.error('Canvas初始化失敗:', error);
                surfaceCanvas = null;
                surfaceCtx = null;
                return false;
            }
        }
        
        function setupEventListeners() {
            try {
                console.log('設置事件監聽器...');

                // 安全的事件監聽器設置函數
                function safeAddEventListener(id, event, handler) {
                    const element = document.getElementById(id);
                    if (element) {
                        element.addEventListener(event, handler);
                        return true;
                    } else {
                        console.warn(`找不到元素: ${id}`);
                        return false;
                    }
                }

                // PID參數滑桿 - 統一使用3位小數
                safeAddEventListener('kp-slider', 'input', (e) => {
                    kp = parseFloat(e.target.value);
                    const valueElement = document.getElementById('kp-value');
                    if (valueElement) valueElement.textContent = kp.toFixed(3);
                    saveParameters(); // 自動保存參數
                });

                safeAddEventListener('ki-slider', 'input', (e) => {
                    ki = parseFloat(e.target.value);
                    const valueElement = document.getElementById('ki-value');
                    if (valueElement) valueElement.textContent = ki.toFixed(3);
                    saveParameters(); // 自動保存參數
                });

                safeAddEventListener('kd-slider', 'input', (e) => {
                    kd = parseFloat(e.target.value);
                    const valueElement = document.getElementById('kd-value');
                    if (valueElement) valueElement.textContent = kd.toFixed(3);
                    saveParameters(); // 自動保存參數
                });

                // 表面參數滑桿
                safeAddEventListener('wave-amplitude', 'input', (e) => {
                    waveAmplitude = parseFloat(e.target.value);
                    const valueElement = document.getElementById('wave-amplitude-value');
                    if (valueElement) valueElement.textContent = waveAmplitude.toFixed(2) + 'mm';
                    saveParameters(); // 自動保存參數
                });

                safeAddEventListener('wave-frequency', 'input', (e) => {
                    waveFrequency = parseInt(e.target.value);
                    const valueElement = document.getElementById('wave-frequency-value');
                    if (valueElement) valueElement.textContent = waveFrequency;
                    saveParameters(); // 自動保存參數
                });

                safeAddEventListener('surface-noise', 'input', (e) => {
                    surfaceNoise = parseFloat(e.target.value);
                    const valueElement = document.getElementById('surface-noise-value');
                    if (valueElement) valueElement.textContent = surfaceNoise.toFixed(2) + 'mm';
                    saveParameters(); // 自動保存參數
                });

                // 移動控制參數
                safeAddEventListener('base-speed', 'input', (e) => {
                    baseSpeed = parseFloat(e.target.value);
                    const valueElement = document.getElementById('base-speed-value');
                    if (valueElement) valueElement.textContent = baseSpeed.toFixed(2) + 'mm/s';
                    saveParameters(); // 自動保存參數
                });

                safeAddEventListener('speed-range', 'input', (e) => {
                    speedRange = parseFloat(e.target.value);
                    const valueElement = document.getElementById('speed-range-value');
                    if (valueElement) valueElement.textContent = '±' + speedRange.toFixed(2) + '%';
                    saveParameters(); // 自動保存參數
                });

                safeAddEventListener('target-tolerance', 'input', (e) => {
                    targetTolerance = parseFloat(e.target.value);
                    const valueElement = document.getElementById('target-tolerance-value');
                    if (valueElement) valueElement.textContent = '±' + targetTolerance.toFixed(2) + 'mm';
                    saveParameters(); // 自動保存參數
                });

                // 流量控制參數
                safeAddEventListener('flow-control-mode', 'change', (e) => {
                    flowControlMode = e.target.value;

                    // 顯示或隱藏手動選擇選項
                    const manualFlowSelection = document.getElementById('manual-flow-selection');
                    if (manualFlowSelection) {
                        if (flowControlMode === 'manual') {
                            manualFlowSelection.style.display = 'flex';
                        } else {
                            manualFlowSelection.style.display = 'none';
                        }
                    }

                    saveParameters(); // 自動保存參數
                });

                safeAddEventListener('flow-high', 'input', (e) => {
                    flowHigh = parseFloat(e.target.value);
                    const valueElement = document.getElementById('flow-high-value');
                    if (valueElement) valueElement.textContent = flowHigh.toFixed(1) + 'cm³/s';
                    saveParameters(); // 自動保存參數
                });

                safeAddEventListener('flow-medium', 'input', (e) => {
                    flowMedium = parseFloat(e.target.value);
                    const valueElement = document.getElementById('flow-medium-value');
                    if (valueElement) valueElement.textContent = flowMedium.toFixed(1) + 'cm³/s';
                    saveParameters(); // 自動保存參數
                });

                safeAddEventListener('flow-low', 'input', (e) => {
                    flowLow = parseFloat(e.target.value);
                    const valueElement = document.getElementById('flow-low-value');
                    if (valueElement) valueElement.textContent = flowLow.toFixed(1) + 'cm³/s';
                    saveParameters(); // 自動保存參數
                });

                // 控制按鈕
                safeAddEventListener('start-btn', 'click', toggleSimulation);
                safeAddEventListener('smart-dispensing-btn', 'click', startSmartDispensing);
                safeAddEventListener('clear-smart-btn', 'click', clearSmartResults);
                safeAddEventListener('quick-10s', 'click', () => quickSimulation(10));
                safeAddEventListener('quick-30s', 'click', () => quickSimulation(30));
                safeAddEventListener('speed-test', 'click', runSpeedTest);
                safeAddEventListener('dispensing-test', 'click', runDispensingTest);
                safeAddEventListener('generate-surface', 'click', generateSurfaceData);

                // 重置縮放按鈕
                safeAddEventListener('reset-zoom', 'click', resetZoom);

                // 圖表縮放功能
                setupChartZoom();

                // 動畫開關
                safeAddEventListener('enable-animation', 'change', (e) => {
                    enableAnimation = e.target.checked;
                    saveParameters(); // 自動保存參數
                });

                // 掃描時間設定
                safeAddEventListener('scan-time-setting', 'input', (e) => {
                    scanTime = parseFloat(e.target.value);
                    updateTimeDisplay();
                    saveParameters(); // 自動保存參數
                });

                // 手動流量選擇
                ['high', 'medium', 'low'].forEach(level => {
                    safeAddEventListener(`manual-flow-${level}`, 'change', (e) => {
                        if (e.target.checked) {
                            manualFlowSelection = level;
                            saveParameters(); // 自動保存參數
                            console.log(`手動選擇流量: ${level}`);
                        }
                    });
                });

                // 智能點膠設定
                safeAddEventListener('smart-iterations', 'input', (e) => {
                    maxSmartIterations = parseInt(e.target.value);
                    const valueElement = document.getElementById('smart-iterations-value');
                    if (valueElement) valueElement.textContent = maxSmartIterations + '次';
                    saveParameters(); // 自動保存參數
                });

                safeAddEventListener('target-pass-rate', 'input', (e) => {
                    targetPassRate = parseInt(e.target.value) / 100; // 轉換為小數
                    const valueElement = document.getElementById('target-pass-rate-value');
                    if (valueElement) valueElement.textContent = Math.round(targetPassRate * 100) + '%';
                    saveParameters(); // 自動保存參數
                });

                // 系統性能設定
                safeAddEventListener('system-load', 'input', (e) => {
                    systemLoad = parseInt(e.target.value);
                    const valueElement = document.getElementById('system-load-value');
                    if (valueElement) valueElement.textContent = systemLoad.toString();
                    saveParameters(); // 自動保存參數
                });

                safeAddEventListener('system-delay', 'input', (e) => {
                    systemDelay = parseFloat(e.target.value);
                    const valueElement = document.getElementById('system-delay-value');
                    if (valueElement) valueElement.textContent = systemDelay.toFixed(1) + '秒';
                    saveParameters(); // 自動保存參數
                });

                // 優化模式選擇
                ['intelligent', 'pid'].forEach(mode => {
                    safeAddEventListener(`mode-${mode}`, 'change', (e) => {
                        if (e.target.checked) {
                            useIntelligentAlgorithm = (mode === 'intelligent');
                            console.log(`切換到${mode === 'intelligent' ? '智能算法' : 'PID優化'}模式`);
                            saveParameters(); // 自動保存參數
                        }
                    });
                });

                // 固定種子開關
                safeAddEventListener('use-fixed-seed', 'change', (e) => {
                    useFixedSeed = e.target.checked;
                    console.log(`${useFixedSeed ? '啟用' : '禁用'}固定種子模式`);
                    if (useFixedSeed) {
                        console.log('🔒 固定種子模式：確保每次生成相同的表面數據和優化結果');
                    } else {
                        console.log('🎲 隨機模式：每次生成不同的表面數據和優化結果');
                    }
                    saveParameters(); // 自動保存參數
                });

                console.log('事件監聽器設置完成');

            } catch (error) {
                console.error('設置事件監聽器時發生錯誤:', error);
                throw error;
            }
        }
        
        function generateSurfaceData() {
            try {
                console.log('開始生成表面數據...');

                // 重置隨機種子確保一致性
                resetRandomSeed();

                // 重置相關變數
                currentPointIndex = 0;
                lastTableUpdateIndex = -1;
                surfaceData = [];
                qualityPass = 0;
                qualityFail = 0;
                totalErrors = [];

                // 驗證參數
                if (totalPoints <= 0 || !Number.isFinite(waveAmplitude) || !Number.isFinite(waveFrequency)) {
                    console.error('Invalid parameters for surface generation');
                    return;
                }

                for (let i = 0; i < totalPoints; i++) {
                    const x = i * (100 / totalPoints); // 0-100mm路徑

                    // 生成原始表面波浪 (相對於0基準，範圍±0.1mm)
                    const wave1 = Math.sin(2 * Math.PI * waveFrequency * i / totalPoints) * waveAmplitude;
                    const wave2 = Math.sin(2 * Math.PI * waveFrequency * 2 * i / totalPoints) * waveAmplitude * 0.3;
                    const noise = (seededRandom() - 0.5) * surfaceNoise * 2; // 使用固定種子

                    // 原始表面變化 (相對高度，範圍-0.1 到 +0.1mm)
                    let surfaceVariation = wave1 + wave2 + noise;
                    surfaceVariation = Math.max(-0.1, Math.min(0.1, surfaceVariation));

                surfaceData.push({
                    index: i,
                    x: x,
                    surfaceVariation: surfaceVariation, // 原始表面變化 (-0.1 到 +0.1mm)
                    originalHeight: surfaceVariation,   // 暫時保持兼容性
                    compensatedHeight: null,
                    isProcessed: false,
                    speed: 0,
                    flow: 0,
                    error: 0
                });
            }

            document.getElementById('point-count').textContent = totalPoints;

            // 調試信息：檢查生成的高度範圍
            const surfaceHeights = surfaceData.map(p => 1.0 + p.surfaceVariation);
            const minHeight = Math.min(...surfaceHeights);
            const maxHeight = Math.max(...surfaceHeights);
            console.log(`表面高度範圍: ${minHeight.toFixed(3)}mm - ${maxHeight.toFixed(3)}mm`);
            console.log(`目標高度: ${targetHeight.toFixed(3)}mm`);
            console.log(`生成了 ${surfaceData.length} 個表面數據點`);

            drawSurfaceChart();
            updateSurfaceDataTable();

            } catch (error) {
                console.error('Error in generateSurfaceData:', error);
                // 生成基本的表面數據作為備用
                surfaceData = [];
                for (let i = 0; i < 100; i++) {
                    surfaceData.push({
                        index: i,
                        x: i,
                        surfaceVariation: 0,
                        originalHeight: 0,
                        compensatedHeight: null,
                        isProcessed: false,
                        speed: 0,
                        flow: 0,
                        error: 0
                    });
                }
                document.getElementById('point-count').textContent = surfaceData.length;
                alert('表面數據生成錯誤，已使用默認平面數據');
            }
        }

        // handleChartClick函數已移除，因為響應曲線圖表已移除
        
        function toggleSimulation() {
            if (isRunning) {
                stopSimulation();
            } else {
                startSimulation();
            }
        }
        
        function startSimulation() {
            try {
                console.log('嘗試開始模擬...');
                console.log(`開始模擬時: targetHeight=${targetHeight}, kp=${kp}`);

                // 自動重置系統
                resetSimulation();

                // 系統健康檢查
                if (!checkSystemHealth()) {
                    console.warn('系統健康檢查失敗，嘗試修復...');
                    initializeCanvas();
                    if (!surfaceData || surfaceData.length === 0) {
                        generateSurfaceData();
                    }

                    // 再次檢查
                    setTimeout(() => {
                        if (checkSystemHealth()) {
                            startSimulation();
                        } else {
                            alert('系統狀態異常，請重新載入頁面');
                        }
                    }, 300);
                    return;
                }

                // 檢測空白圖表
                if (!detectAndFixBlankChart()) {
                    console.warn('檢測到空白圖表，等待修復完成...');
                    setTimeout(() => {
                        startSimulation(); // 重試
                    }, 200);
                    return;
                }

                // 檢查系統狀態
                if (isRunning) {
                    console.log('模擬已在運行中');
                    return;
                }

                // 檢查Canvas是否有效
                if (!surfaceCtx) {
                    console.warn('Canvas無效，重新初始化...');
                    initializeCanvas();
                    if (!surfaceCtx) {
                        alert('Canvas初始化失敗，請重新載入頁面');
                        return;
                    }
                }

                // 檢查是否有表面數據
                if (!surfaceData || surfaceData.length === 0) {
                    console.warn('沒有表面數據，重新生成...');
                    generateSurfaceData();
                    setTimeout(() => {
                        if (surfaceData && surfaceData.length > 0) {
                            startSimulation(); // 遞歸重試
                        } else {
                            alert('表面數據生成失敗，請手動點擊"生成表面數據"按鈕');
                        }
                    }, 200);
                    return;
                }

                // 檢查數據完整性
                let validDataCount = 0;
                surfaceData.forEach(point => {
                    if (point && typeof point.surfaceVariation === 'number') {
                        validDataCount++;
                    }
                });

                if (validDataCount < surfaceData.length * 0.9) { // 至少90%的數據有效
                    console.warn('表面數據不完整，重新生成...');
                    generateSurfaceData();
                    return;
                }

            // 如果已經完成所有點，重置到開始
            if (currentPointIndex >= surfaceData.length) {
                currentPointIndex = 0;
                // 重置處理狀態
                if (surfaceData && Array.isArray(surfaceData)) {
                    surfaceData.forEach(point => {
                        if (point) {
                            point.compensatedHeight = null;
                            point.isProcessed = false;
                            point.speed = 0;
                            point.flow = 0;
                            point.error = 0;
                        }
                    });
                }
                qualityPass = 0;
                qualityFail = 0;
                totalErrors = [];

                // 清空時間序列數據
                timeData = [];
                heightData = [];
                targetData = [];
                speedData = [];
                flowData = [];

                // 強制重新繪製圖表以清除之前的結果
                setTimeout(() => {
                    console.log('重新開始模擬，清除之前的結果...');

                    // 確保Canvas有效
                    if (!surfaceCtx) {
                        console.warn('Canvas無效，重新初始化...');
                        initializeCanvas();
                    }

                    // 重新繪製圖表
                    const chartDrawn = drawSurfaceChart();
                    if (!chartDrawn) {
                        console.warn('圖表繪製失敗，嘗試修復...');
                        setTimeout(() => {
                            initializeCanvas();
                            drawSurfaceChart();
                        }, 50);
                    }

                    updateSurfaceDataTable();
                    console.log('重新開始模擬準備完成');
                }, 50); // 增加延遲確保重置完成
            }

            isRunning = true;
            startTime = performance.now();
            document.getElementById('start-btn').textContent = '⏸️ 暫停模擬';
            document.getElementById('start-btn').className = 'btn btn-warning';

            // 開始塗膠時間統計（只在非智能計算階段）
            if (!isInSmartCalculation) {
                startDispensingTimer();
            }

            animate();

            } catch (error) {
                console.error('開始模擬時發生錯誤:', error);
                stopSimulation();
                alert('開始模擬失敗: ' + error.message + '\n請嘗試重置系統或重新載入頁面');
            }
        }
        
        function stopSimulation() {
            isRunning = false;
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
            document.getElementById('start-btn').textContent = '▶️ 開始模擬';
            document.getElementById('start-btn').className = 'btn btn-primary';

            // 結束塗膠時間統計
            endDispensingTimer();
        }
        
        function resetSimulation() {
            try {
                console.log('開始重置系統...');
                stopSimulation();

                // 確保動畫完全停止
                if (animationId) {
                    cancelAnimationFrame(animationId);
                    animationId = null;
                }

                // 重置所有控制變數
                currentPointIndex = 0;
                lastTableUpdateIndex = -1;
                lastError = 0;
                integral = 0;
                currentSpeed = baseSpeed;
                currentFlow = 0;
                isRunning = false;
                startTime = null;

                // 重置高度變數 - 應該從基準表面高度開始
                currentHeight = 1.0; // 始終從基準表面高度開始
                console.log(`重置後 currentHeight = ${currentHeight}`);

            // 重置表面數據處理狀態
            if (surfaceData && Array.isArray(surfaceData) && surfaceData.length > 0) {
                if (smartDispensingCompleted && smartOptimizedData) {
                    // 如果智能點膠已完成，恢復優化數據而不是重置
                    console.log('智能點膠已完成，恢復優化數據而不是重置');
                    surfaceData.forEach((point, index) => {
                        if (point && smartOptimizedData[index]) {
                            const optimizedPoint = smartOptimizedData[index];
                            point.compensatedHeight = optimizedPoint.compensatedHeight;
                            point.isProcessed = false; // 重置處理狀態以便重新模擬
                            point.speed = optimizedPoint.speed;
                            point.flow = optimizedPoint.flow;
                            point.error = optimizedPoint.error;
                            point.pidOutput = optimizedPoint.pidOutput;
                        }
                    });
                    console.log('智能算法優化數據已恢復');
                } else {
                    // 正常重置
                    console.log(`重置 ${surfaceData.length} 個表面數據點的狀態`);
                    surfaceData.forEach((point, index) => {
                        if (point && typeof point === 'object') {
                            point.compensatedHeight = null;
                            point.isProcessed = false;
                            point.speed = 0;
                            point.flow = 0;
                            point.error = 0;
                            point.pidOutput = 0;
                        } else {
                            console.warn(`表面數據點 ${index} 無效:`, point);
                        }
                    });
                }
            } else {
                console.warn('表面數據無效，重新生成...');
                generateSurfaceData();
                // 等待數據生成完成
                setTimeout(() => {
                    console.log('表面數據重新生成完成');
                }, 100);
            }

            // 重置品質統計
            qualityPass = 0;
            qualityFail = 0;
            totalErrors = [];

            // 清空數據
            timeData = [];
            heightData = [];
            targetData = [];
            speedData = [];
            flowData = [];

            // 重置UI狀態
            const startBtn = document.getElementById('start-btn');
            if (startBtn) {
                startBtn.textContent = '▶️ 開始模擬';
                startBtn.className = 'btn btn-success';
            }

            // 強制重新繪製所有圖表 (分步驟執行)
            setTimeout(() => {
                try {
                    console.log('開始重新繪製流程...');

                    // 第一步：確保Canvas有效
                    if (!surfaceCtx) {
                        console.log('重新初始化Canvas...');
                        if (!initializeCanvas()) {
                            throw new Error('Canvas初始化失敗');
                        }
                    }

                    // 第二步：確保數據有效
                    if (!surfaceData || surfaceData.length === 0) {
                        console.log('重新生成表面數據...');
                        generateSurfaceData();

                        // 等待數據生成完成後再繪製
                        setTimeout(() => {
                            if (surfaceData && surfaceData.length > 0) {
                                console.log('數據生成完成，開始繪製...');
                                updateDisplay();
                                updateSurfaceLegend();
                                if (!drawSurfaceChart()) {
                                    console.error('圖表繪製失敗');
                                }
                                updateSurfaceDataTable();
                            }
                        }, 100);
                        return;
                    }

                    // 第三步：重新繪製
                    console.log('重新繪製所有圖表...');
                    updateDisplay();
                    updateSurfaceLegend();

                    // 確保圖表繪製成功
                    const chartDrawn = drawSurfaceChart();
                    if (!chartDrawn) {
                        console.warn('圖表繪製失敗，嘗試重新初始化...');
                        initializeCanvas();
                        setTimeout(() => drawSurfaceChart(), 50);
                    }

                    updateSurfaceDataTable();

                    console.log('系統重置完成，表面數據點數:', surfaceData.length);

                    // 驗證圖表是否有內容
                    setTimeout(() => {
                        if (!detectAndFixBlankChart()) {
                            console.warn('檢測到空白圖表，強制重新繪製...');
                            drawSurfaceChart();
                        }
                    }, 200);

                } catch (error) {
                    console.error('重置過程中發生錯誤:', error);
                    // 最後的恢復嘗試
                    setTimeout(() => {
                        console.log('執行最後的恢復嘗試...');
                        initializeCanvas();
                        generateSurfaceData();
                        setTimeout(() => {
                            drawSurfaceChart();
                            updateSurfaceDataTable();
                        }, 200);
                    }, 300);
                }
            }, 150); // 增加延遲確保DOM完全更新

            } catch (error) {
                console.error('Error in resetSimulation:', error);
                // 嘗試基本重置
                currentPointIndex = 0;
                currentHeight = 1.0;
                isRunning = false;
                alert('重置過程中發生錯誤，請重新載入頁面');
            }
        }
        
        // 智能優化算法 - 基於表面高度數據計算最佳補償
        function calculateIntelligentCompensation() {
            console.log('🧠 開始智能優化算法計算...');

            if (!surfaceData || surfaceData.length === 0) {
                console.error('沒有表面數據');
                return false;
            }

            pointCompensationMap.clear();

            // 分析表面高度分布
            const surfaceHeights = surfaceData.map(point => 1.0 + point.surfaceVariation);
            const minHeight = Math.min(...surfaceHeights);
            const maxHeight = Math.max(...surfaceHeights);
            const avgHeight = surfaceHeights.reduce((sum, h) => sum + h, 0) / surfaceHeights.length;

            console.log(`表面分析: 最低=${minHeight.toFixed(3)}mm, 最高=${maxHeight.toFixed(3)}mm, 平均=${avgHeight.toFixed(3)}mm`);

            // 為每個點計算最佳補償量
            surfaceData.forEach((point, index) => {
                const originalHeight = 1.0 + point.surfaceVariation;
                const heightDiff = targetHeight - originalHeight;

                // 基礎補償：直接補償到目標高度
                let baseCompensation = heightDiff;

                // 考慮鄰近點的影響 (平滑化)
                let neighborInfluence = 0;
                let neighborCount = 0;

                // 檢查前後各2個點
                for (let offset = -2; offset <= 2; offset++) {
                    const neighborIndex = index + offset;
                    if (neighborIndex >= 0 && neighborIndex < surfaceData.length && neighborIndex !== index) {
                        const neighborHeight = 1.0 + surfaceData[neighborIndex].surfaceVariation;
                        const neighborDiff = targetHeight - neighborHeight;
                        neighborInfluence += neighborDiff;
                        neighborCount++;
                    }
                }

                if (neighborCount > 0) {
                    const avgNeighborDiff = neighborInfluence / neighborCount;
                    // 平滑化：30%考慮鄰近點，70%考慮當前點
                    baseCompensation = baseCompensation * 0.7 + avgNeighborDiff * 0.3;
                }

                // 考慮表面整體趨勢
                const trendFactor = (originalHeight - avgHeight) / (maxHeight - minHeight + 0.001);
                const trendCompensation = -trendFactor * 0.02; // 輕微的趨勢補償

                // 最終補償量
                let finalCompensation = baseCompensation + trendCompensation;

                // 限制補償範圍，避免過度補償
                const maxCompensation = Math.abs(heightDiff) * 1.2; // 最多補償120%
                finalCompensation = Math.max(-maxCompensation, Math.min(maxCompensation, finalCompensation));

                // 存儲補償量
                pointCompensationMap.set(index, finalCompensation);

                if (index % 20 === 0) {
                    console.log(`點${index}: 原始=${originalHeight.toFixed(3)}, 目標=${targetHeight.toFixed(3)}, 補償=${finalCompensation.toFixed(3)}`);
                }
            });

            console.log(`✅ 智能算法完成，計算了${surfaceData.length}個點的補償量`);
            return true;
        }

        // 應用智能補償到指定點
        function applyIntelligentCompensation(pointIndex, originalHeight) {
            if (!pointCompensationMap.has(pointIndex)) {
                return originalHeight; // 沒有補償數據，返回原始高度
            }

            const compensation = pointCompensationMap.get(pointIndex);

            // 模擬實際點膠過程的不確定性
            // 即使有智能補償，也不可能100%精確
            const processNoise = (Math.random() - 0.5) * 0.02; // ±0.01mm的過程噪聲
            const systemError = (Math.random() - 0.5) * 0.01; // ±0.005mm的系統誤差

            let compensatedHeight = originalHeight + compensation + processNoise + systemError;

            // 考慮實際設備的物理限制
            // 速度和流量調整的效果有限
            const maxCompensationEffect = 0.8; // 最多只能補償80%的誤差
            const actualCompensation = compensation * maxCompensationEffect;
            compensatedHeight = originalHeight + actualCompensation + processNoise + systemError;

            // 確保補償後的高度在物理可能的範圍內
            const minAllowedHeight = targetHeight - targetTolerance * 3;
            const maxAllowedHeight = targetHeight + targetTolerance * 3;

            return Math.max(minAllowedHeight, Math.min(maxAllowedHeight, compensatedHeight));
        }

        // 評估智能算法的性能
        function evaluateIntelligentAlgorithm() {
            if (pointCompensationMap.size === 0) {
                return { passRate: 0, avgError: 1, maxError: 1 };
            }

            let passCount = 0;
            let totalError = 0;
            let maxError = 0;

            surfaceData.forEach((point, index) => {
                const originalHeight = 1.0 + point.surfaceVariation;
                const compensatedHeight = applyIntelligentCompensation(index, originalHeight);
                const error = Math.abs(compensatedHeight - targetHeight);

                totalError += error;
                maxError = Math.max(maxError, error);

                if (error <= targetTolerance) {
                    passCount++;
                }
            });

            const passRate = passCount / surfaceData.length;
            const avgError = totalError / surfaceData.length;

            console.log(`🎯 智能算法評估: 達標率=${(passRate*100).toFixed(1)}%, 平均誤差=${avgError.toFixed(3)}mm, 最大誤差=${maxError.toFixed(3)}mm`);

            return { passRate, avgError, maxError, passCount, totalCount: surfaceData.length };
        }

        // 應用智能算法結果
        function applyIntelligentResults(result) {
            // 模擬應用智能補償到所有點
            surfaceData.forEach((point, index) => {
                const originalHeight = 1.0 + point.surfaceVariation;
                const compensatedHeight = applyIntelligentCompensation(index, originalHeight);

                // 更新點數據
                point.compensatedHeight = compensatedHeight;
                point.isProcessed = true;
                point.error = Math.abs(compensatedHeight - targetHeight);

                // 記錄智能算法參數（用於顯示）
                point.kp = 0; // 智能算法不使用PID
                point.ki = 0;
                point.kd = 0;
                point.speed = baseSpeed; // 使用基礎速度
                point.flow = flowMedium; // 使用中等流量
            });

            // 更新圖表和數據表
            drawSurfaceChart();
            updateSurfaceDataTable();

            // 更新統計
            qualityPass = result.passCount;
            qualityFail = result.totalCount - result.passCount;

            // 記錄到優化歷史
            const intelligentRecord = {
                iteration: 1,
                kp: 0, ki: 0, kd: 0, // 智能算法不使用PID
                baseSpeed: baseSpeed, speedRange: speedRange,
                flowHigh: flowHigh, flowMedium: flowMedium, flowLow: flowLow,
                passCount: result.passCount,
                processedCount: result.totalCount,
                passRate: result.passRate,
                avgError: result.avgError,
                maxError: result.maxError,
                overshootCount: 0,
                undershootCount: 0,
                overshootRate: 0,
                undershootRate: 0,
                performance: result.passRate,
                algorithm: 'intelligent' // 標記為智能算法
            };

            optimizationHistory.push(intelligentRecord);
            bestPerformance = result.passRate;

            console.log('✅ 智能算法結果已應用到所有點');
        }

        // 智能點膠功能
        function startSmartDispensing() {
            try {
                console.log('開始智能點膠...');

                if (isSmartDispensing) {
                    // 停止智能點膠
                    stopSmartDispensing();
                    return;
                }

                // 初始化智能點膠
                isSmartDispensing = true;
                smartIterationCount = 0;
                bestPerformance = 0; // 達標率，越高越好
                optimizationHistory = [];

                // 重置時間統計
                resetTimeStats();

                // 設置智能計算階段標誌
                isInSmartCalculation = true;

                // 開始計算時間統計
                startCalcTimer();

                // 檢查是否使用智能算法
                if (surfaceData && surfaceData.length > 0) {
                    console.log('🧠 使用智能優化算法模式');

                    // 計算智能補償
                    if (calculateIntelligentCompensation()) {
                        // 評估智能算法性能
                        const intelligentResult = evaluateIntelligentAlgorithm();

                        if (intelligentResult.passRate >= targetPassRate) {
                            console.log(`🎉 智能算法已達標！達標率: ${(intelligentResult.passRate * 100).toFixed(1)}%`);

                            // 直接應用智能算法結果
                            applyIntelligentResults(intelligentResult);
                            stopSmartDispensing();
                            return;
                        } else {
                            console.log(`智能算法初步結果: 達標率${(intelligentResult.passRate * 100).toFixed(1)}%，需要進一步優化`);
                        }
                    }
                }

                // 如果智能算法未達標，則使用傳統PID優化
                console.log('🔄 使用傳統PID參數優化模式');
                useIntelligentAlgorithm = false;

                // 設定保守的初始參數，避免超調
                if (kp > 2.5) {
                    console.log(`初始Kp過高 (${kp.toFixed(2)})，調整為保守值`);
                    kp = 1.5; // 設定保守的初始Kp
                    ki = 0.1; // 適中的積分項
                    kd = 0.05; // 小的微分項
                    updateUIFromParameters(); // 更新UI顯示
                }

                // 記錄初始參數
                bestParams = {
                    kp: kp, ki: ki, kd: kd,
                    baseSpeed: baseSpeed, speedRange: speedRange,
                    flowHigh: flowHigh, flowMedium: flowMedium, flowLow: flowLow
                };

                // 更新按鈕狀態
                const smartBtn = document.getElementById('smart-dispensing-btn');
                if (smartBtn) {
                    smartBtn.textContent = '⏹️ 停止智能點膠';
                    smartBtn.className = 'btn btn-danger';
                }

                // 更新狀態顯示
                const smartStatus = document.getElementById('smart-status');
                if (smartStatus) {
                    smartStatus.textContent = '初始化中...';
                    smartStatus.style.color = '#f39c12';
                }

                // 開始第一次迭代
                runSmartIteration();

            } catch (error) {
                console.error('智能點膠啟動失敗:', error);
                stopSmartDispensing();
            }
        }

        function stopSmartDispensing() {
            isSmartDispensing = false;

            // 結束智能計算階段
            isInSmartCalculation = false;

            // 結束所有計時器
            endCalcTimer();
            endDispensingTimer();

            stopSimulation();

            // 恢復按鈕狀態
            const smartBtn = document.getElementById('smart-dispensing-btn');
            if (smartBtn) {
                smartBtn.textContent = '🧠 智能點膠';
                smartBtn.className = 'btn btn-success';
            }

            // 更新狀態顯示
            const smartStatus = document.getElementById('smart-status');
            if (smartStatus) {
                smartStatus.textContent = '待機';
                smartStatus.style.color = '#9b59b6';
            }

            // 顯示最佳參數
            if (optimizationHistory.length > 0) {
                console.log('智能點膠完成！');
                console.log('最佳參數:', bestParams);
                console.log('最佳達標率:', (bestPerformance * 100).toFixed(1) + '%');

                // 應用最佳參數
                console.log(`智能點膠完成前: targetHeight=${targetHeight}, kp=${kp}`);
                kp = bestParams.kp;
                ki = bestParams.ki;
                kd = bestParams.kd;
                baseSpeed = bestParams.baseSpeed;
                speedRange = bestParams.speedRange;
                flowHigh = bestParams.flowHigh;
                flowMedium = bestParams.flowMedium;
                flowLow = bestParams.flowLow;
                console.log(`智能點膠完成後: targetHeight=${targetHeight}, kp=${kp}`);

                updateUIFromParameters();

                const passRatePercent = (bestPerformance * 100).toFixed(1);
                const targetMet = bestPerformance >= targetPassRate ? '✅ 達標' : '❌ 未達標';

                // 保存最佳參數到UI，確保可重現
                kp = bestParams.kp;
                ki = bestParams.ki;
                kd = bestParams.kd;
                baseSpeed = bestParams.baseSpeed;
                speedRange = bestParams.speedRange;
                flowHigh = bestParams.flowHigh;
                flowMedium = bestParams.flowMedium;
                flowLow = bestParams.flowLow;
                updateUIFromParameters();
                saveParameters(); // 自動保存最佳參數

                // 標記智能點膠已完成並保存優化數據
                smartDispensingCompleted = true;
                smartOptimizedData = surfaceData.map(point => ({
                    x: point.x,
                    y: point.y,
                    surfaceVariation: point.surfaceVariation,
                    compensatedHeight: point.compensatedHeight,
                    isProcessed: point.isProcessed,
                    speed: point.speed,
                    flow: point.flow,
                    error: point.error,
                    pidOutput: point.pidOutput
                }));
                console.log('智能算法優化數據已保存，包含', smartOptimizedData.length, '個點');

                // 顯示清除智能結果按鈕
                const clearSmartBtn = document.getElementById('clear-smart-btn');
                if (clearSmartBtn) {
                    clearSmartBtn.style.display = 'inline-block';
                }

                // 顯示趨勢圖
                showSmartTrendChart();

                alert(`智能點膠完成！${targetMet}\n\n最佳參數組合：\n` +
                      `PID: Kp=${bestParams.kp.toFixed(2)}, Ki=${bestParams.ki.toFixed(3)}, Kd=${bestParams.kd.toFixed(3)}\n` +
                      `速度: ${bestParams.baseSpeed.toFixed(2)}mm/s, 範圍±${bestParams.speedRange.toFixed(2)}%\n` +
                      `流量: 大${bestParams.flowHigh.toFixed(2)}, 中${bestParams.flowMedium.toFixed(2)}, 小${bestParams.flowLow.toFixed(2)}\n\n` +
                      `達標率: ${passRatePercent}% (目標: ${(targetPassRate*100)}%)\n\n` +
                      `✅ 最佳參數已自動應用到UI，現在用相同參數模擬應該得到相同結果！\n\n` +
                      `💡 如需重新開始，可點擊"清除智能結果"按鈕\n\n` +
                      `點擊確定後將顯示優化趨勢圖`);
            }
        }

        // 清除智能點膠結果
        function clearSmartResults() {
            if (confirm('確定要清除智能點膠的優化結果嗎？\n這將重置所有參數和數據到初始狀態。')) {
                // 重置智能點膠狀態
                smartDispensingCompleted = false;
                smartOptimizedData = null;
                optimizationHistory = [];
                bestPerformance = 0;

                // 隱藏清除按鈕
                const clearSmartBtn = document.getElementById('clear-smart-btn');
                if (clearSmartBtn) {
                    clearSmartBtn.style.display = 'none';
                }

                // 重置表面數據
                if (surfaceData && Array.isArray(surfaceData)) {
                    surfaceData.forEach(point => {
                        if (point) {
                            point.compensatedHeight = null;
                            point.isProcessed = false;
                            point.speed = 0;
                            point.flow = 0;
                            point.error = 0;
                            point.pidOutput = 0;
                        }
                    });
                }

                // 重新繪製圖表
                drawSurfaceChart();
                updateSurfaceDataTable();

                console.log('智能點膠結果已清除');
                alert('智能點膠結果已清除！\n系統已重置到初始狀態。');
            }
        }

        function runSmartIteration() {
            if (!isSmartDispensing) return;

            smartIterationCount++;
            console.log(`智能點膠迭代 ${smartIterationCount}/${maxSmartIterations}`);

            // 更新狀態顯示
            const smartStatus = document.getElementById('smart-status');
            if (smartStatus) {
                smartStatus.textContent = `迭代 ${smartIterationCount}/${maxSmartIterations}`;
                smartStatus.style.color = '#e74c3c';
            }

            // 生成新的PID參數
            generateNewPidParams();

            // 更新UI顯示當前參數
            updateUIFromParameters();

            // 根據動畫開關決定模擬方式
            if (enableAnimation) {
                // 啟用動畫：使用正常模擬
                console.log('使用動畫模擬模式');
                startSimulation();

                // 設定模擬完成檢查
                const checkCompletion = () => {
                    if (!isRunning && isSmartDispensing) {
                        evaluatePerformance();

                        // 檢查是否已達到目標或完成所有迭代
                        if (bestPerformance >= targetPassRate) {
                            console.log(`🎉 提前達標！達標率: ${(bestPerformance * 100).toFixed(1)}% (目標: ${(targetPassRate * 100).toFixed(0)}%)`);
                            stopSmartDispensing();
                        } else if (smartIterationCount < maxSmartIterations) {
                            // 繼續下一次迭代
                            setTimeout(() => runSmartIteration(), 1000);
                        } else {
                            // 完成所有迭代
                            completeSmartDispensing();
                        }
                    } else if (isSmartDispensing) {
                        // 繼續檢查
                        setTimeout(checkCompletion, 500);
                    }
                };
                setTimeout(checkCompletion, 1000);
            } else {
                // 關閉動畫：使用快速模擬
                console.log('使用快速模擬模式');
                startFastSimulation();

                // 設定模擬完成後的回調 (快速模擬)
                setTimeout(() => {
                    if (isSmartDispensing) {
                        evaluatePerformance();

                        // 檢查是否已達到目標或完成所有迭代
                        if (bestPerformance >= targetPassRate) {
                            console.log(`🎉 提前達標！達標率: ${(bestPerformance * 100).toFixed(1)}% (目標: ${(targetPassRate * 100).toFixed(0)}%)`);
                            stopSmartDispensing();
                        } else if (smartIterationCount < maxSmartIterations) {
                            // 繼續下一次迭代
                            setTimeout(() => runSmartIteration(), 500);
                        } else {
                            // 完成所有迭代
                            completeSmartDispensing();
                        }
                    }
                }, 2000); // 快速模擬只需2秒
            }
        }

        // 品質管制檢查函數
        function checkQualityStandards(testKp, testKi, testKd) {
            // 模擬一個簡單的測試來預測性能
            const predictedOvershoot = testKp * 0.2; // 簡化的超調預測
            const predictedSettlingTime = 1 / (testKi + 0.01); // 簡化的穩定時間預測

            // 品質標準檢查
            if (predictedOvershoot > 0.6) { // 預測超調過大
                return { pass: false, reason: `Kp=${testKp.toFixed(2)}可能導致超調` };
            }

            if (testKp < 0.3) { // Kp過小可能響應不足
                return { pass: false, reason: `Kp=${testKp.toFixed(2)}可能響應不足` };
            }

            if (testKi > 0.25) { // Ki過大可能導致振盪
                return { pass: false, reason: `Ki=${testKi.toFixed(3)}可能導致振盪` };
            }

            return { pass: true, reason: '參數符合品質標準' };
        }

        function generateNewPidParams() {
            if (smartIterationCount === 1) {
                // 第一次使用當前參數
                return;
            }

            // 使用改進的遺傳算法生成新參數
            const mutationRate = 0.4; // 增大變異率
            const crossoverRate = 0.6;

            if (optimizationHistory.length > 0 && seededRandom() < crossoverRate) {
                // 基於歷史最佳參數進行交叉變異
                const baseParams = bestParams;

                // PID參數變異
                kp = baseParams.kp * (1 + (seededRandom() - 0.5) * mutationRate);
                ki = baseParams.ki * (1 + (seededRandom() - 0.5) * mutationRate);
                kd = baseParams.kd * (1 + (seededRandom() - 0.5) * mutationRate);

                // 速度參數變異
                baseSpeed = baseParams.baseSpeed * (1 + (seededRandom() - 0.5) * mutationRate * 0.5);
                speedRange = baseParams.speedRange * (1 + (seededRandom() - 0.5) * mutationRate * 0.3);

                // 流量參數變異
                flowHigh = baseParams.flowHigh * (1 + (seededRandom() - 0.5) * mutationRate * 0.3);
                flowMedium = baseParams.flowMedium * (1 + (seededRandom() - 0.5) * mutationRate * 0.3);
                flowLow = baseParams.flowLow * (1 + (seededRandom() - 0.5) * mutationRate * 0.3);

            } else {
                // 隨機生成新參數 - 針對膠高控制優化範圍，降低Kp避免超調
                kp = seededRandom() * 2.0 + 0.5; // 0.5-2.5，降低範圍避免超調
                ki = seededRandom() * 0.2 + 0.02; // 0.02-0.22，適中的積分項
                kd = seededRandom() * 0.1 + 0.01; // 0.01-0.11，較小的微分項

                // 速度參數 - 偏向較慢的速度以提高精度
                baseSpeed = seededRandom() * 30 + 10; // 10-40mm/s
                speedRange = seededRandom() * 60 + 20; // 20-80%

                // 流量參數 - 確保合理的流量梯度
                flowHigh = seededRandom() * 1.0 + 1.2; // 1.2-2.2
                flowMedium = seededRandom() * 0.6 + 0.7; // 0.7-1.3
                flowLow = seededRandom() * 0.4 + 0.3; // 0.3-0.7
            }

            // 限制參數範圍 - 更嚴格的控制避免超調
            kp = Math.max(0.3, Math.min(3.0, kp)); // 降低Kp上限從8.0到3.0
            ki = Math.max(0.01, Math.min(0.3, ki)); // 適中的積分項
            kd = Math.max(0.005, Math.min(0.15, kd)); // 較小的微分項

            baseSpeed = Math.max(5, Math.min(50, baseSpeed));
            speedRange = Math.max(10, Math.min(90, speedRange));

            flowHigh = Math.max(1.0, Math.min(2.5, flowHigh));
            flowMedium = Math.max(0.5, Math.min(1.8, flowMedium));
            flowLow = Math.max(0.2, Math.min(1.2, flowLow));

            // 確保流量遞減關係
            if (flowMedium >= flowHigh) flowMedium = flowHigh * 0.8;
            if (flowLow >= flowMedium) flowLow = flowMedium * 0.7;

            // 品質管制檢查
            const qualityCheck = checkQualityStandards(kp, ki, kd);

            console.log(`新參數組合:`);
            console.log(`PID: Kp=${kp.toFixed(2)}, Ki=${ki.toFixed(3)}, Kd=${kd.toFixed(3)}`);
            console.log(`速度: ${baseSpeed.toFixed(2)}mm/s, 範圍±${speedRange.toFixed(2)}%`);
            console.log(`流量: 大${flowHigh.toFixed(2)}, 中${flowMedium.toFixed(2)}, 小${flowLow.toFixed(2)}`);
            console.log(`品質檢查: ${qualityCheck.pass ? '✅' : '⚠️'} ${qualityCheck.reason}`);
        }

        function evaluatePerformance() {
            if (!surfaceData || surfaceData.length === 0) return;

            // 計算達標率和性能指標
            let passCount = 0;
            let totalError = 0;
            let maxError = 0;
            let processedCount = 0;
            let overshootCount = 0; // 超調點數
            let undershootCount = 0; // 欠調點數

            surfaceData.forEach(point => {
                if (point.isProcessed && point.compensatedHeight !== null) {
                    const error = Math.abs(point.compensatedHeight - targetHeight);
                    const heightDiff = point.compensatedHeight - targetHeight;

                    totalError += error;
                    maxError = Math.max(maxError, error);
                    processedCount++;

                    // 檢查是否在目標公差範圍內 (1.2mm ± 0.05mm)
                    if (error <= targetTolerance) {
                        passCount++;
                    } else {
                        // 統計超調和欠調
                        if (heightDiff > targetTolerance) {
                            overshootCount++; // 超出上限
                        } else if (heightDiff < -targetTolerance) {
                            undershootCount++; // 低於下限
                        }
                    }
                }
            });

            if (processedCount === 0) return;

            const passRate = passCount / processedCount; // 達標率
            const avgError = totalError / processedCount;
            const overshootRate = overshootCount / processedCount; // 超調率
            const undershootRate = undershootCount / processedCount; // 欠調率

            // 改進的性能評分：達標率70%，平均誤差20%，超調懲罰10%
            let performance = passRate * 0.7 + (1 - Math.min(avgError / 0.1, 1)) * 0.2;

            // 超調懲罰 - 超調比欠調更嚴重
            const overshootPenalty = overshootRate * 0.15; // 超調懲罰係數
            const undershootPenalty = undershootRate * 0.05; // 欠調懲罰係數較小
            performance = performance - overshootPenalty - undershootPenalty;

            // 確保性能分數不為負
            performance = Math.max(0, performance);

            // 記錄結果
            const result = {
                iteration: smartIterationCount,
                kp: kp, ki: ki, kd: kd,
                baseSpeed: baseSpeed, speedRange: speedRange,
                flowHigh: flowHigh, flowMedium: flowMedium, flowLow: flowLow,
                passCount: passCount,
                processedCount: processedCount,
                passRate: passRate,
                avgError: avgError,
                maxError: maxError,
                overshootCount: overshootCount,
                undershootCount: undershootCount,
                overshootRate: overshootRate,
                undershootRate: undershootRate,
                performance: performance
            };

            optimizationHistory.push(result);

            // 更新最佳參數
            if (performance > bestPerformance) {
                bestPerformance = performance;
                bestParams = {
                    kp: kp, ki: ki, kd: kd,
                    baseSpeed: baseSpeed, speedRange: speedRange,
                    flowHigh: flowHigh, flowMedium: flowMedium, flowLow: flowLow
                };
                console.log(`🎯 發現更佳參數組合！達標率: ${(passRate * 100).toFixed(1)}%, 性能: ${performance.toFixed(3)}`);
            }

            console.log(`迭代 ${smartIterationCount} 結果:`);
            console.log(`  達標: ${passCount}/${processedCount} (${(passRate * 100).toFixed(1)}%)`);
            console.log(`  平均誤差: ${avgError.toFixed(3)}mm, 最大誤差: ${maxError.toFixed(3)}mm`);
            console.log(`  超調: ${overshootCount}點 (${(overshootRate * 100).toFixed(1)}%), 欠調: ${undershootCount}點 (${(undershootRate * 100).toFixed(1)}%)`);
            console.log(`  綜合性能: ${performance.toFixed(3)} (達標率權重70%, 誤差權重20%, 超調懲罰)`);

            // 如果超調率過高，給出警告
            if (overshootRate > 0.2) {
                console.warn(`⚠️ 超調率過高 (${(overshootRate * 100).toFixed(1)}%)，Kp可能過大`);
            }
        }

        // 完成智能點膠
        function completeSmartDispensing() {
            console.log(`完成所有 ${maxSmartIterations} 次迭代`);

            // 結束計算時間統計
            endCalcTimer();

            // 結束智能計算階段
            isInSmartCalculation = false;

            // 應用最佳參數
            if (bestParams) {
                kp = bestParams.kp;
                ki = bestParams.ki;
                kd = bestParams.kd;
                baseSpeed = bestParams.baseSpeed;
                speedRange = bestParams.speedRange;
                flowHigh = bestParams.flowHigh;
                flowMedium = bestParams.flowMedium;
                flowLow = bestParams.flowLow;

                updateUIFromParameters();
            }

            // 顯示最終結果
            const bestResult = optimizationHistory.find(r => r.performance === bestPerformance) || optimizationHistory[optimizationHistory.length - 1];
            const targetMet = bestResult.passRate >= targetPassRate ? '✅ 達標' : '❌ 未達標';
            const passRatePercent = (bestResult.passRate * 100).toFixed(1);

            // 顯示趨勢圖
            showSmartTrendChart();

            alert(`智能點膠完成！${targetMet}\n\n最佳參數組合：\n` +
                  `PID: Kp=${bestParams.kp.toFixed(3)}, Ki=${bestParams.ki.toFixed(3)}, Kd=${bestParams.kd.toFixed(3)}\n` +
                  `速度: ${bestParams.baseSpeed.toFixed(2)}mm/s, 範圍±${bestParams.speedRange.toFixed(2)}%\n` +
                  `流量: 大${bestParams.flowHigh.toFixed(2)}, 中${bestParams.flowMedium.toFixed(2)}, 小${bestParams.flowLow.toFixed(2)}\n\n` +
                  `達標率: ${passRatePercent}% (目標: ${(targetPassRate*100)}%)\n\n` +
                  `✅ 最佳參數已自動應用到UI，現在用相同參數模擬應該得到相同結果！\n\n` +
                  `💡 如需重新開始，可點擊"清除智能結果"按鈕\n\n` +
                  `🎬 動畫模式: ${enableAnimation ? '已啟用' : '已關閉'}`);

            stopSmartDispensing();
        }

        // 時間顯示更新函數
        function updateTimeDisplay() {
            totalTime = scanTime + calcTime + dispensingTime;

            const scanTimeElement = document.getElementById('scan-time-display');
            const calcTimeElement = document.getElementById('calc-time-display');
            const dispensingTimeElement = document.getElementById('dispensing-time-display');
            const totalTimeElement = document.getElementById('total-time-display');

            if (scanTimeElement) scanTimeElement.textContent = scanTime.toFixed(1) + '秒';
            if (calcTimeElement) calcTimeElement.textContent = calcTime.toFixed(1) + '秒';
            if (dispensingTimeElement) dispensingTimeElement.textContent = dispensingTime.toFixed(1) + '秒';
            if (totalTimeElement) totalTimeElement.textContent = totalTime.toFixed(1) + '秒';
        }

        // 重置時間統計
        function resetTimeStats() {
            calcTime = 0.0;
            dispensingTime = 0.0;
            updateTimeDisplay();
        }

        // 開始計算時間統計
        function startCalcTimer() {
            calcStartTime = performance.now();
        }

        // 結束計算時間統計
        function endCalcTimer() {
            if (calcStartTime > 0) {
                calcTime += (performance.now() - calcStartTime) / 1000; // 轉換為秒
                calcStartTime = 0;
                updateTimeDisplay();
            }
        }

        // 開始塗膠時間統計
        function startDispensingTimer() {
            // 如果在智能點膠計算階段，不啟動塗膠計時
            if (isInSmartCalculation) {
                console.log('智能計算階段，跳過塗膠計時');
                return;
            }
            dispensingStartTime = performance.now();
        }

        // 結束塗膠時間統計
        function endDispensingTimer() {
            if (dispensingStartTime > 0) {
                dispensingTime += (performance.now() - dispensingStartTime) / 1000; // 轉換為秒
                dispensingStartTime = 0;
                updateTimeDisplay();
            }
        }

        // 塗膠測試函數（純塗膠時間測試）
        function runDispensingTest() {
            if (isRunning || isSmartDispensing) {
                alert('請先停止當前的模擬或智能點膠');
                return;
            }

            // 重置塗膠時間
            dispensingTime = 0.0;
            updateTimeDisplay();

            // 確保不在智能計算階段
            isInSmartCalculation = false;

            // 開始塗膠測試
            console.log('開始塗膠測試...');
            startDispensingTimer();

            // 運行正常模擬來測試塗膠時間
            startSimulation();

            // 更新按鈕狀態
            const testBtn = document.getElementById('dispensing-test');
            if (testBtn) {
                testBtn.textContent = '⏹️ 停止測試';
                testBtn.className = 'btn btn-danger';

                // 添加停止測試功能
                testBtn.onclick = () => {
                    stopSimulation();
                    testBtn.textContent = '🧪 塗膠測試';
                    testBtn.className = 'btn btn-outline-primary';
                    testBtn.onclick = () => runDispensingTest();
                    console.log(`塗膠測試完成，塗膠時間: ${dispensingTime.toFixed(1)}秒`);
                };
            }
        }

        // 快速模擬函數 (用於智能點膠，無動畫)
        function startFastSimulation() {
            try {
                console.log('開始快速模擬 (無動畫)...');

                // 自動重置系統
                resetSimulation();

                if (surfaceData.length === 0) {
                    console.error('沒有表面數據');
                    return;
                }

                // 設定快速模擬標誌
                isRunning = true;

                // 開始塗膠時間統計（快速模擬）
                startDispensingTimer();

                // 重置PID變數
                lastError = 0;
                integral = 0;
                currentPointIndex = 0;
                qualityPass = 0;
                qualityFail = 0;
                totalErrors = [];

                // 快速處理所有點 (無動畫)
                const dt = 0.1; // 固定時間步長

                while (currentPointIndex < surfaceData.length && isRunning) {
                    updatePID(dt);

                    // 每10個點更新一次圖表 (減少繪製次數)
                    if (currentPointIndex % 10 === 0) {
                        drawSurfaceChart();
                        updateSurfaceDataTable();
                    }
                }

                // 最終更新
                drawSurfaceChart();
                updateSurfaceDataTable();

                console.log(`快速模擬完成: 處理了 ${currentPointIndex} 個點`);

                // 結束塗膠時間統計
                endDispensingTimer();

            } catch (error) {
                console.error('快速模擬失敗:', error);
                endDispensingTimer(); // 確保在錯誤時也結束計時
                stopSimulation();
            }
        }

        // 智能點膠趨勢圖相關函數
        function showSmartTrendChart() {
            if (optimizationHistory.length === 0) {
                alert('沒有優化歷史數據可顯示');
                return;
            }

            // 顯示模態框
            document.getElementById('smart-trend-modal').style.display = 'block';

            // 繪製趨勢圖
            drawSmartTrendChart();

            // 生成總結
            generateSmartTrendSummary();
        }

        function closeSmartTrendModal() {
            document.getElementById('smart-trend-modal').style.display = 'none';
        }

        function drawSmartTrendChart() {
            const canvas = document.getElementById('smart-trend-chart');
            const ctx = canvas.getContext('2d');

            // 清空畫布
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 設定繪圖區域
            const padding = 60;
            const chartWidth = canvas.width - 2 * padding;
            const chartHeight = canvas.height - 2 * padding;

            // 繪製背景
            ctx.fillStyle = '#f8f9fa';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // 繪製圖表背景
            ctx.fillStyle = 'white';
            ctx.fillRect(padding, padding, chartWidth, chartHeight);

            // 繪製網格
            ctx.strokeStyle = '#e9ecef';
            ctx.lineWidth = 1;

            // 垂直網格線
            for (let i = 0; i <= 10; i++) {
                const x = padding + (i / 10) * chartWidth;
                ctx.beginPath();
                ctx.moveTo(x, padding);
                ctx.lineTo(x, padding + chartHeight);
                ctx.stroke();
            }

            // 水平網格線
            for (let i = 0; i <= 10; i++) {
                const y = padding + (i / 10) * chartHeight;
                ctx.beginPath();
                ctx.moveTo(padding, y);
                ctx.lineTo(padding + chartWidth, y);
                ctx.stroke();
            }

            // 繪製達標率趨勢線
            drawTrendLine(ctx, optimizationHistory, 'passRate', '#27ae60', '達標率', padding, chartWidth, chartHeight);

            // 繪製平均誤差趨勢線
            drawTrendLine(ctx, optimizationHistory, 'avgError', '#e74c3c', '平均誤差', padding, chartWidth, chartHeight, true);

            // 繪製軸標籤
            drawChartLabels(ctx, canvas.width, canvas.height, padding);

            // 繪製圖例
            drawChartLegend(ctx, canvas.width, padding);
        }

        function drawTrendLine(ctx, data, property, color, label, padding, chartWidth, chartHeight, isError = false) {
            if (data.length < 2) return;

            // 計算數據範圍
            let minVal, maxVal;
            if (isError) {
                // 誤差數據
                const values = data.map(d => d[property]);
                minVal = Math.min(...values);
                maxVal = Math.max(...values);
                const range = maxVal - minVal;
                minVal = Math.max(0, minVal - range * 0.1);
                maxVal = maxVal + range * 0.1;
            } else {
                // 達標率數據 (0-1)
                minVal = 0;
                maxVal = 1;
            }

            // 先繪製趨勢線
            ctx.strokeStyle = color;
            ctx.lineWidth = 3;
            ctx.beginPath();

            data.forEach((point, index) => {
                const x = padding + (index / (data.length - 1)) * chartWidth;
                const normalizedValue = (point[property] - minVal) / (maxVal - minVal);
                const y = padding + chartHeight - (normalizedValue * chartHeight);

                if (index === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            });

            ctx.stroke(); // 先繪製線條

            // 再繪製數據點
            ctx.fillStyle = color;
            data.forEach((point, index) => {
                const x = padding + (index / (data.length - 1)) * chartWidth;
                const normalizedValue = (point[property] - minVal) / (maxVal - minVal);
                const y = padding + chartHeight - (normalizedValue * chartHeight);

                ctx.beginPath();
                ctx.arc(x, y, 4, 0, 2 * Math.PI);
                ctx.fill();
            });
        }

        function drawChartLabels(ctx, canvasWidth, canvasHeight, padding) {
            ctx.fillStyle = '#2c3e50';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';

            // X軸標籤
            ctx.fillText('迭代次數', canvasWidth / 2, canvasHeight - 20);

            // Y軸標籤
            ctx.save();
            ctx.translate(20, canvasHeight / 2);
            ctx.rotate(-Math.PI / 2);
            ctx.fillText('達標率 / 平均誤差', 0, 0);
            ctx.restore();

            // 標題
            ctx.font = 'bold 18px Arial';
            ctx.fillText('智能點膠優化趨勢', canvasWidth / 2, 30);
        }

        function drawChartLegend(ctx, canvasWidth, padding) {
            ctx.font = '12px Arial';

            // 達標率圖例
            ctx.fillStyle = '#27ae60';
            ctx.fillRect(canvasWidth - 150, padding + 20, 15, 15);
            ctx.fillStyle = '#2c3e50';
            ctx.textAlign = 'left';
            ctx.fillText('達標率', canvasWidth - 130, padding + 32);

            // 平均誤差圖例
            ctx.fillStyle = '#e74c3c';
            ctx.fillRect(canvasWidth - 150, padding + 45, 15, 15);
            ctx.fillStyle = '#2c3e50';
            ctx.fillText('平均誤差', canvasWidth - 130, padding + 57);
        }

        function generateSmartTrendSummary() {
            const summaryDiv = document.getElementById('smart-trend-summary');

            if (optimizationHistory.length === 0) {
                summaryDiv.innerHTML = '<p>沒有優化數據</p>';
                return;
            }

            // 計算統計數據
            const finalResult = optimizationHistory[optimizationHistory.length - 1];
            const bestResult = optimizationHistory.reduce((best, current) =>
                current.passRate > best.passRate ? current : best
            );

            const initialPassRate = optimizationHistory[0].passRate;
            const finalPassRate = finalResult.passRate;
            const improvement = ((finalPassRate - initialPassRate) / initialPassRate * 100).toFixed(1);

            summaryDiv.innerHTML = `
                <h3 style="margin-top: 0; color: #2c3e50;">📊 優化總結</h3>
                <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px;">
                    <div style="background: white; padding: 15px; border-radius: 5px; border-left: 4px solid #3498db;">
                        <h4 style="margin: 0 0 10px 0; color: #3498db;">初始性能</h4>
                        <p style="margin: 5px 0;">達標率: ${(initialPassRate * 100).toFixed(1)}%</p>
                        <p style="margin: 5px 0;">平均誤差: ${optimizationHistory[0].avgError.toFixed(3)}mm</p>
                    </div>
                    <div style="background: white; padding: 15px; border-radius: 5px; border-left: 4px solid #27ae60;">
                        <h4 style="margin: 0 0 10px 0; color: #27ae60;">最佳性能</h4>
                        <p style="margin: 5px 0;">達標率: ${(bestResult.passRate * 100).toFixed(1)}%</p>
                        <p style="margin: 5px 0;">平均誤差: ${bestResult.avgError.toFixed(3)}mm</p>
                        <p style="margin: 5px 0;">迭代: ${bestResult.iteration}</p>
                    </div>
                    <div style="background: white; padding: 15px; border-radius: 5px; border-left: 4px solid #e74c3c;">
                        <h4 style="margin: 0 0 10px 0; color: #e74c3c;">最終結果</h4>
                        <p style="margin: 5px 0;">達標率: ${(finalPassRate * 100).toFixed(1)}%</p>
                        <p style="margin: 5px 0;">改善: ${improvement}%</p>
                        <p style="margin: 5px 0;">總迭代: ${optimizationHistory.length}</p>
                    </div>
                </div>
                <div style="margin-top: 20px; background: white; padding: 15px; border-radius: 5px;">
                    <h4 style="margin: 0 0 10px 0; color: #2c3e50;">🎯 最佳參數組合</h4>
                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px;">
                        <div>
                            <strong>PID參數:</strong><br>
                            Kp: ${bestResult.kp.toFixed(2)}<br>
                            Ki: ${bestResult.ki.toFixed(3)}<br>
                            Kd: ${bestResult.kd.toFixed(3)}
                        </div>
                        <div>
                            <strong>速度參數:</strong><br>
                            基礎速度: ${bestResult.baseSpeed.toFixed(2)}mm/s<br>
                            調整範圍: ±${bestResult.speedRange.toFixed(2)}%
                        </div>
                        <div>
                            <strong>流量參數:</strong><br>
                            大流量: ${bestResult.flowHigh.toFixed(2)}cm³/s<br>
                            中流量: ${bestResult.flowMedium.toFixed(2)}cm³/s<br>
                            小流量: ${bestResult.flowLow.toFixed(2)}cm³/s
                        </div>
                    </div>
                </div>
            `;
        }

        // 速度影響測試功能
        function runSpeedTest() {
            if (isRunning) {
                alert('請先停止當前模擬');
                return;
            }

            if (surfaceData.length === 0) {
                alert('請先生成表面數據');
                return;
            }

            // 保存當前參數
            const originalBaseSpeed = baseSpeed;
            const originalSpeedRange = speedRange;

            // 測試不同速度設定的效果
            const testResults = [];

            console.log('🚀 開始速度影響測試...');

            // 測試1: 極慢速度
            baseSpeed = 5;
            speedRange = 20;
            const slowResult = calculateSpeedEffect();
            testResults.push({ name: '極慢速度', baseSpeed: 5, speedRange: 20, ...slowResult });

            // 測試2: 中等速度
            baseSpeed = 25;
            speedRange = 50;
            const mediumResult = calculateSpeedEffect();
            testResults.push({ name: '中等速度', baseSpeed: 25, speedRange: 50, ...mediumResult });

            // 測試3: 極快速度
            baseSpeed = 80;
            speedRange = 80;
            const fastResult = calculateSpeedEffect();
            testResults.push({ name: '極快速度', baseSpeed: 80, speedRange: 80, ...fastResult });

            // 恢復原始參數
            baseSpeed = originalBaseSpeed;
            speedRange = originalSpeedRange;
            updateUIFromParameters();

            // 顯示測試結果
            showSpeedTestResults(testResults);
        }

        function calculateSpeedEffect() {
            // 模擬計算速度對膠高的影響
            let totalSpeedCompensation = 0;
            let pointCount = 0;

            // 模擬前10個點的速度效果
            for (let i = 0; i < Math.min(10, surfaceData.length); i++) {
                const point = surfaceData[i];
                const originalSurfaceHeight = 1.0 + point.surfaceVariation;
                const heightError = targetHeight - originalSurfaceHeight;

                // 模擬速度計算 (簡化版)
                const absError = Math.abs(heightError);
                let errorSpeedFactor = 1.0;
                if (absError > targetTolerance) {
                    errorSpeedFactor = 0.7; // 降速
                } else {
                    errorSpeedFactor = 1.2; // 提速
                }

                const currentSpeed = baseSpeed * errorSpeedFactor;
                const speedRatio = currentSpeed / baseSpeed;
                const speedInfluence = speedRange / 100;

                let speedCompensation = 0;
                if (speedRatio < 1.0) {
                    const slownessFactor = (1.0 - speedRatio);
                    speedCompensation = slownessFactor * 0.3 * speedInfluence;
                } else if (speedRatio > 1.0) {
                    const fastnessFactor = (speedRatio - 1.0);
                    speedCompensation = -fastnessFactor * 0.2 * speedInfluence;
                }

                const baseSpeedEffect = (30 - baseSpeed) / 100;
                speedCompensation += baseSpeedEffect * 0.1;

                totalSpeedCompensation += speedCompensation;
                pointCount++;
            }

            const avgSpeedCompensation = totalSpeedCompensation / pointCount;
            const avgSpeed = baseSpeed * 0.9; // 假設平均速度

            return {
                avgSpeedCompensation: avgSpeedCompensation,
                avgSpeed: avgSpeed,
                speedRange: speedRange
            };
        }

        function showSpeedTestResults(results) {
            let message = '🚀 速度影響測試結果：\n\n';

            results.forEach(result => {
                const compensationMm = result.avgSpeedCompensation.toFixed(3);
                const sign = result.avgSpeedCompensation >= 0 ? '+' : '';
                message += `${result.name}:\n`;
                message += `  基礎速度: ${result.baseSpeed}mm/s\n`;
                message += `  速度範圍: ±${result.speedRange}%\n`;
                message += `  平均速度補償: ${sign}${compensationMm}mm\n`;
                message += `  膠高影響: ${result.avgSpeedCompensation > 0 ? '增加' : '減少'}\n\n`;
            });

            message += '💡 說明：\n';
            message += '• 速度慢 → 膠高增加（正值）\n';
            message += '• 速度快 → 膠高減少（負值）\n';
            message += '• 速度範圍越大 → 影響越明顯\n\n';
            message += '現在請調整移動控制參數，觀察實際效果！';

            alert(message);

            // 在控制台輸出詳細結果
            console.log('📊 速度測試詳細結果:');
            results.forEach(result => {
                console.log(`${result.name}: 基礎速度=${result.baseSpeed}mm/s, 補償=${result.avgSpeedCompensation.toFixed(3)}mm`);
            });
        }

        function quickSimulation(seconds) {
            const originalSpeed = 1;
            const quickSpeed = 50; // 50倍速
            
            if (!isRunning) {
                startSimulation();
            }
            
            // 暫時加速模擬
            const quickInterval = setInterval(() => {
                for (let i = 0; i < quickSpeed; i++) {
                    updatePID(0.02); // 20ms時間步長
                }
            }, 20);
            
            // 在指定時間後恢復正常速度
            setTimeout(() => {
                clearInterval(quickInterval);
            }, seconds * 1000 / quickSpeed);
        }
        
        function animate() {
            try {
                if (!isRunning) return;

                const currentTime = performance.now();
                let deltaTime = startTime ? (currentTime - startTime) / 1000 : 0;
                startTime = currentTime;

                // 系統延遲影響響應時間
                deltaTime = deltaTime + (systemDelay * 0.01); // 延遲影響時間步長

                updatePID(Math.min(deltaTime, 0.05)); // 限制最大時間步長
                updateDisplay();
                drawSurfaceChart(); // 動態更新表面圖表

                // 只有當處理新的點時才更新表格，避免過度重繪
                if (currentPointIndex !== lastTableUpdateIndex) {
                    updateSurfaceDataTable();
                    lastTableUpdateIndex = currentPointIndex;
                }

                if (isRunning) { // 再次檢查狀態
                    animationId = requestAnimationFrame(animate);
                }

            } catch (error) {
                console.error('Error in animate:', error);
                stopSimulation();
                alert('模擬過程中發生錯誤，已停止模擬');
            }
        }
        
        // 智能算法更新函數
        function updateWithIntelligentAlgorithm(dt) {
            const currentPoint = surfaceData[currentPointIndex];
            const originalSurfaceHeight = 1.0 + currentPoint.surfaceVariation;

            // 使用預計算的智能補償
            const compensatedHeight = applyIntelligentCompensation(currentPointIndex, originalSurfaceHeight);

            // 更新當前點數據
            currentPoint.compensatedHeight = compensatedHeight;
            currentPoint.isProcessed = true;
            currentPoint.error = Math.abs(compensatedHeight - targetHeight);

            // 更新全局當前高度變數，用於顯示
            currentHeight = compensatedHeight;

            // 智能算法根據表面高度動態調整速度和流量
            const heightError = targetHeight - originalSurfaceHeight;
            const absError = Math.abs(heightError);

            // 根據高度誤差動態調整速度
            let adaptiveSpeed = baseSpeed;
            if (absError > targetTolerance * 2) {
                // 大誤差：降低速度以提高精度
                adaptiveSpeed = baseSpeed * (1 - speedRange / 100 * 0.8);
            } else if (absError > targetTolerance) {
                // 中等誤差：適度調整速度
                adaptiveSpeed = baseSpeed * (1 - speedRange / 100 * 0.4);
            } else {
                // 小誤差：可以使用較高速度
                adaptiveSpeed = baseSpeed * (1 + speedRange / 100 * 0.2);
            }

            // 根據高度誤差動態調整流量
            let adaptiveFlow = flowMedium;
            if (heightError > targetTolerance) {
                // 需要更多膠量：使用大流量
                adaptiveFlow = flowHigh;
            } else if (heightError < -targetTolerance) {
                // 需要較少膠量：使用小流量
                adaptiveFlow = flowLow;
            } else {
                // 誤差在公差內：使用中等流量
                adaptiveFlow = flowMedium;
            }

            // 記錄智能算法參數
            currentPoint.kp = 0; // 智能算法不使用傳統PID
            currentPoint.ki = 0;
            currentPoint.kd = 0;
            currentPoint.speed = adaptiveSpeed;
            currentPoint.flow = adaptiveFlow;
            currentPoint.pidOutput = heightError; // 記錄高度誤差作為"智能輸出"

            // 品質評估
            if (currentPoint.error <= targetTolerance) {
                qualityPass++;
            } else {
                qualityFail++;
            }
            totalErrors.push(currentPoint.error);

            // 更新實時達標率顯示
            const currentPassRateElement = document.getElementById('current-pass-rate');
            if (currentPassRateElement) {
                const totalProcessed = qualityPass + qualityFail;
                const passRate = totalProcessed > 0 ? (qualityPass / totalProcessed) * 100 : 0;
                currentPassRateElement.textContent = passRate.toFixed(1) + '%';

                // 根據達標率設定顏色
                if (passRate >= 90) {
                    currentPassRateElement.style.color = '#27ae60'; // 綠色 - 優秀
                } else if (passRate >= 80) {
                    currentPassRateElement.style.color = '#f39c12'; // 橙色 - 良好
                } else if (passRate >= 70) {
                    currentPassRateElement.style.color = '#e67e22'; // 橙紅 - 一般
                } else {
                    currentPassRateElement.style.color = '#e74c3c'; // 紅色 - 需改善
                }
            }

            // 更新進度顯示
            const progressElement = document.getElementById('current-point');
            if (progressElement) {
                progressElement.textContent = `${currentPointIndex + 1}/${surfaceData.length}`;
            }

            // 調試信息 (每10個點輸出一次)
            if (currentPointIndex % 10 === 0) {
                console.log(`智能算法 Point ${currentPointIndex}: 原始=${originalSurfaceHeight.toFixed(3)}, 補償後=${compensatedHeight.toFixed(3)}, 誤差=${currentPoint.error.toFixed(3)}`);
            }

            // 移動到下一個點
            currentPointIndex++;

            // 檢查是否完成所有點
            if (currentPointIndex >= surfaceData.length) {
                stopSimulation();
                return;
            }

            // 更新圖表和數據表 (每5個點更新一次)
            if (currentPointIndex % 5 === 0 || currentPointIndex >= surfaceData.length) {
                drawSurfaceChart();
                updateSurfaceDataTable();
            }
        }

        function updatePID(dt) {
            if (surfaceData.length === 0) {
                return;
            }

            if (currentPointIndex >= surfaceData.length) {
                stopSimulation();
                return;
            }

            // 檢查是否使用智能算法模式
            if (isSmartDispensing && useIntelligentAlgorithm && pointCompensationMap.size > 0) {
                updateWithIntelligentAlgorithm(dt);
                return;
            }

            const currentPoint = surfaceData[currentPointIndex];

            // 獲取當前表面變化 (相對於0基準)
            const surfaceVariation = currentPoint.surfaceVariation;

            // 計算原始表面絕對高度
            const originalSurfaceHeight = 1.0 + surfaceVariation;

            // 計算高度誤差 (目標高度 - 原始表面高度)
            // 這個誤差表示需要補償多少才能達到目標高度
            const heightError = targetHeight - originalSurfaceHeight;

            // PID控制計算
            integral += heightError * dt;
            const derivative = (heightError - lastError) / dt;

            // 計算各項PID分量
            const proportionalTerm = kp * heightError;
            const integralTerm = ki * integral;
            const derivativeTerm = kd * derivative;
            let pidOutput = proportionalTerm + integralTerm + derivativeTerm;

            // 系統負載影響PID性能
            const loadFactor = 1 + (systemLoad / 100); // 負載越高，響應越慢
            pidOutput = pidOutput / loadFactor;

            // 更新系統性能顯示
            const systemLoadElement = document.getElementById('current-system-load');
            const systemDelayElement = document.getElementById('current-system-delay');
            if (systemLoadElement) {
                systemLoadElement.textContent = systemLoad.toString();
                // 根據負載設定顏色
                if (systemLoad > 30) {
                    systemLoadElement.style.color = '#e74c3c'; // 紅色 - 高負載
                } else if (systemLoad > 20) {
                    systemLoadElement.style.color = '#f39c12'; // 橙色 - 中負載
                } else {
                    systemLoadElement.style.color = '#27ae60'; // 綠色 - 低負載
                }
            }
            if (systemDelayElement) {
                systemDelayElement.textContent = systemDelay.toFixed(1) + 's';
                // 根據延遲設定顏色
                if (systemDelay > 3) {
                    systemDelayElement.style.color = '#e74c3c'; // 紅色 - 高延遲
                } else if (systemDelay > 1.5) {
                    systemDelayElement.style.color = '#f39c12'; // 橙色 - 中延遲
                } else {
                    systemDelayElement.style.color = '#27ae60'; // 綠色 - 低延遲
                }
            }

            // 調試信息 (每10個點輸出一次)
            if (currentPointIndex % 10 === 0) {
                console.log(`Point ${currentPointIndex}: Error=${heightError.toFixed(3)}, P=${proportionalTerm.toFixed(3)}, I=${integralTerm.toFixed(3)}, D=${derivativeTerm.toFixed(3)}, PID=${pidOutput.toFixed(3)}, Load=${systemLoad}, Delay=${systemDelay.toFixed(1)}s`);
            }

            // 根據PID輸出和誤差調整移動速度
            // 使用更明顯的速度控制邏輯
            const absError = Math.abs(heightError);
            const absPidOutput = Math.abs(pidOutput);

            // 基於誤差大小的速度調整 (主要因素) - 增強敏感度
            let errorSpeedFactor = 1.0;
            if (absError > targetTolerance * 3) {
                // 誤差極大時，極大幅降低速度
                errorSpeedFactor = 0.2; // 降到20%
            } else if (absError > targetTolerance * 2) {
                // 誤差很大時，大幅降低速度
                errorSpeedFactor = 0.3; // 降到30%
            } else if (absError > targetTolerance * 1.5) {
                // 誤差大時，降低速度
                errorSpeedFactor = 0.5; // 降到50%
            } else if (absError > targetTolerance) {
                // 誤差中等時，適度降低速度
                errorSpeedFactor = 0.7; // 降到70%
            } else if (absError < targetTolerance * 0.3) {
                // 誤差很小時，大幅提高速度
                errorSpeedFactor = 1.5; // 提高到150%
            } else if (absError < targetTolerance * 0.7) {
                // 誤差小時，適度提高速度
                errorSpeedFactor = 1.2; // 提高到120%
            } else {
                // 誤差接近公差時，正常速度
                errorSpeedFactor = 1.0; // 100%
            }

            // 基於PID輸出的速度微調 (輔助因素) - 增強影響
            let pidSpeedFactor = 1.0;
            if (absPidOutput > 0.3) {
                pidSpeedFactor = 0.6; // PID輸出很大時大幅降速
            } else if (absPidOutput > 0.15) {
                pidSpeedFactor = 0.8; // PID輸出大時降速
            } else if (absPidOutput < 0.03) {
                pidSpeedFactor = 1.3; // PID輸出很小時提速
            } else if (absPidOutput < 0.08) {
                pidSpeedFactor = 1.1; // PID輸出小時稍微提速
            }

            // speedRange參數直接影響速度調整的強度 - 大幅增強影響
            const rangeInfluence = speedRange / 100; // 將speedRange轉換為影響係數

            // 增強speedRange的影響，讓變化更明顯
            if (speedRange <= 10) {
                // 極小範圍：幾乎不調整速度
                errorSpeedFactor = 1.0 + (errorSpeedFactor - 1.0) * 0.05;
                pidSpeedFactor = 1.0 + (pidSpeedFactor - 1.0) * 0.05;
            } else if (speedRange >= 80) {
                // 極大範圍：大幅調整速度
                errorSpeedFactor = 1.0 + (errorSpeedFactor - 1.0) * 3.0; // 從2.0增加到3.0
                pidSpeedFactor = 1.0 + (pidSpeedFactor - 1.0) * 3.0;
            } else {
                // 正常範圍：按比例調整，增強係數
                const enhancedInfluence = rangeInfluence * 1.5; // 增強1.5倍
                errorSpeedFactor = 1.0 + (errorSpeedFactor - 1.0) * enhancedInfluence;
                pidSpeedFactor = 1.0 + (pidSpeedFactor - 1.0) * enhancedInfluence;
            }

            // 計算最終速度
            let targetSpeed = baseSpeed * errorSpeedFactor * pidSpeedFactor;

            // 應用速度範圍限制
            const minSpeed = baseSpeed * (1 - speedRange/100);
            const maxSpeed = baseSpeed * (1 + speedRange/100);
            currentSpeed = Math.max(minSpeed, Math.min(maxSpeed, targetSpeed));

            // 更新速度調整顯示
            const speedAdjustmentElement = document.getElementById('speed-adjustment');
            if (speedAdjustmentElement) {
                const speedPercentage = (currentSpeed / baseSpeed) * 100;
                speedAdjustmentElement.textContent = speedPercentage.toFixed(0) + '%';

                // 根據速度調整程度設定顏色
                if (speedPercentage < 50) {
                    speedAdjustmentElement.style.color = '#e74c3c'; // 紅色 - 大幅降速
                } else if (speedPercentage < 80) {
                    speedAdjustmentElement.style.color = '#f39c12'; // 橙色 - 適度降速
                } else if (speedPercentage > 120) {
                    speedAdjustmentElement.style.color = '#27ae60'; // 綠色 - 提速
                } else {
                    speedAdjustmentElement.style.color = '#3498db'; // 藍色 - 正常
                }
            }

            // 更新速度範圍顯示
            const speedRangeElement = document.getElementById('speed-range-display');
            if (speedRangeElement) {
                const minSpeed = baseSpeed * (1 - speedRange/100);
                const maxSpeed = baseSpeed * (1 + speedRange/100);
                speedRangeElement.textContent = `${minSpeed.toFixed(0)}-${maxSpeed.toFixed(0)}mm/s`;

                // 根據當前速度在範圍中的位置設定顏色
                if (currentSpeed <= minSpeed + (maxSpeed - minSpeed) * 0.3) {
                    speedRangeElement.style.color = '#e74c3c'; // 紅色 - 低速區
                } else if (currentSpeed >= minSpeed + (maxSpeed - minSpeed) * 0.7) {
                    speedRangeElement.style.color = '#27ae60'; // 綠色 - 高速區
                } else {
                    speedRangeElement.style.color = '#8e44ad'; // 紫色 - 中速區
                }
            }

            // 調試信息 (每10個點輸出一次)
            if (currentPointIndex % 10 === 0) {
                console.log(`Speed Control: Error=${absError.toFixed(3)}, ErrorFactor=${errorSpeedFactor.toFixed(2)}, PIDFactor=${pidSpeedFactor.toFixed(2)}, Speed=${currentSpeed.toFixed(1)}mm/s (${((currentSpeed/baseSpeed)*100).toFixed(0)}%)`);
            }

            // 根據流量控制模式選擇流量段位
            if (flowControlMode === 'auto') {
                const absError = Math.abs(heightError);
                // 自動模式：根據誤差大小選擇流量段位
                // 調整閾值使流量切換更合理
                if (absError > 0.15) { // 大於0.15mm誤差使用大流量
                    currentFlow = flowHigh;
                    document.getElementById('current-flow').textContent = '大流量(自動)';
                } else if (absError > 0.05) { // 0.05-0.15mm誤差使用中流量
                    currentFlow = flowMedium;
                    document.getElementById('current-flow').textContent = '中流量(自動)';
                } else { // 小於0.05mm誤差使用小流量
                    currentFlow = flowLow;
                    document.getElementById('current-flow').textContent = '小流量(自動)';
                }

                // 添加調試信息
                if (currentPointIndex % 10 === 0) {
                    console.log(`Flow Control: Error=${absError.toFixed(3)}mm, Flow=${currentFlow.toFixed(1)}cm³/s`);
                }
            } else if (flowControlMode === 'manual') {
                // 手動模式：使用用戶選擇的固定流量段位
                switch (manualFlowSelection) {
                    case 'high':
                        currentFlow = flowHigh;
                        document.getElementById('current-flow').textContent = '大流量(手動)';
                        break;
                    case 'medium':
                        currentFlow = flowMedium;
                        document.getElementById('current-flow').textContent = '中流量(手動)';
                        break;
                    case 'low':
                        currentFlow = flowLow;
                        document.getElementById('current-flow').textContent = '小流量(手動)';
                        break;
                    default:
                        currentFlow = flowMedium;
                        document.getElementById('current-flow').textContent = '中流量(手動)';
                }
            } else if (flowControlMode === 'off') {
                currentFlow = 0;
                document.getElementById('current-flow').textContent = '關閉';
            }

            // 計算補償效果
            // PID輸出影響補償量
            const pidCompensation = pidOutput * 0.3; // PID基礎補償

            // 速度對膠高的影響 - 大幅增強影響係數
            let speedCompensation = 0;
            const speedRatio = currentSpeed / baseSpeed; // 當前速度相對於基礎速度的比例

            // 計算速度影響的基礎係數 (基於speedRange參數)
            const speedInfluence = speedRange / 100; // speedRange越大，速度影響越明顯

            if (speedRatio < 1.0) {
                // 速度比基礎速度慢時，膠水有更多時間流出，膠高增加
                const slownessFactor = (1.0 - speedRatio); // 0到1之間，越慢值越大
                speedCompensation = slownessFactor * 0.3 * speedInfluence; // 大幅增加影響係數

                // 極慢速度時額外增強效果
                if (speedRatio < 0.5) {
                    speedCompensation += slownessFactor * 0.2; // 額外增加
                }
            } else if (speedRatio > 1.0) {
                // 速度比基礎速度快時，膠水流出時間減少，膠高減少
                const fastnessFactor = (speedRatio - 1.0); // 大於0，越快值越大
                speedCompensation = -fastnessFactor * 0.2 * speedInfluence; // 增加影響係數

                // 極快速度時額外增強效果
                if (speedRatio > 1.5) {
                    speedCompensation -= fastnessFactor * 0.15; // 額外減少
                }
            }

            // 基礎速度本身的影響 - 低速度整體增加膠高
            const baseSpeedEffect = (30 - baseSpeed) / 100; // 基礎速度越低，膠高越高
            speedCompensation += baseSpeedEffect * 0.1;

            // 調試信息
            if (currentPointIndex % 10 === 0) {
                console.log(`Speed Effect: BaseSpeed=${baseSpeed.toFixed(1)}, Ratio=${speedRatio.toFixed(2)}, Influence=${speedInfluence.toFixed(2)}, Compensation=${speedCompensation.toFixed(3)}mm`);
            }

            // 流量補償 - 大幅增加影響係數
            let flowCompensation = 0;
            if (currentFlow > 0) {
                // 流量補償基於流量值，係數進一步增大
                const baseFlowEffect = currentFlow * 0.25; // 從0.15增加到0.25

                // 根據誤差方向和大小調整補償
                if (Math.abs(heightError) > targetTolerance) {
                    // 誤差大時，流量補償更積極
                    if (heightError > 0) {
                        // 需要增加高度時，流量正向補償
                        flowCompensation = baseFlowEffect * 1.5;
                    } else {
                        // 需要減少高度時，流量負向補償（減少塗膠）
                        flowCompensation = -baseFlowEffect * 0.8;
                    }
                } else {
                    // 誤差小時，流量補償較溫和
                    if (heightError > 0) {
                        flowCompensation = baseFlowEffect * 0.8;
                    } else {
                        flowCompensation = -baseFlowEffect * 0.4;
                    }
                }

                // 根據流量段位進一步調整
                let flowMultiplier = 1.0;
                if (currentFlow >= flowHigh * 0.9) {
                    flowMultiplier = 1.5; // 大流量時效果更強
                } else if (currentFlow >= flowMedium * 0.9) {
                    flowMultiplier = 1.2; // 中流量時效果中等
                } else {
                    flowMultiplier = 0.8; // 小流量時效果較弱
                }

                flowCompensation *= flowMultiplier;

                // 調試信息
                if (currentPointIndex % 5 === 0) {
                    console.log(`Flow Control: Flow=${currentFlow.toFixed(1)}, Error=${heightError.toFixed(3)}, Compensation=${flowCompensation.toFixed(3)}, Multiplier=${flowMultiplier.toFixed(1)}`);
                }
            }

            // 計算補償後的最終高度
            // 原始表面高度 + PID補償 + 流量補償 + 速度補償
            const compensatedHeight = originalSurfaceHeight + pidCompensation + flowCompensation + speedCompensation;

            // 更新流量補償顯示
            const flowCompensationElement = document.getElementById('flow-compensation');
            if (flowCompensationElement) {
                const sign = flowCompensation >= 0 ? '+' : '';
                flowCompensationElement.textContent = sign + flowCompensation.toFixed(3) + 'mm';
                flowCompensationElement.style.color = flowCompensation >= 0 ? '#16a085' : '#e74c3c';
            }

            // 更新速度補償顯示
            const speedCompensationElement = document.getElementById('speed-compensation');
            if (speedCompensationElement) {
                const sign = speedCompensation >= 0 ? '+' : '';
                speedCompensationElement.textContent = sign + speedCompensation.toFixed(3) + 'mm';

                // 根據補償大小設定顏色和強調
                if (Math.abs(speedCompensation) > 0.1) {
                    speedCompensationElement.style.color = speedCompensation >= 0 ? '#27ae60' : '#e74c3c';
                    speedCompensationElement.style.fontWeight = 'bold'; // 大補償時加粗
                } else if (Math.abs(speedCompensation) > 0.05) {
                    speedCompensationElement.style.color = speedCompensation >= 0 ? '#2ecc71' : '#e67e22';
                    speedCompensationElement.style.fontWeight = 'normal';
                } else {
                    speedCompensationElement.style.color = '#95a5a6'; // 灰色表示影響很小
                    speedCompensationElement.style.fontWeight = 'normal';
                }
            }

            // 更新流量效果比例顯示
            const flowEffectElement = document.getElementById('flow-effect-ratio');
            if (flowEffectElement) {
                const totalCompensation = Math.abs(pidCompensation) + Math.abs(flowCompensation) + Math.abs(speedCompensation);
                if (totalCompensation > 0) {
                    const flowRatio = (Math.abs(flowCompensation) / totalCompensation) * 100;
                    flowEffectElement.textContent = flowRatio.toFixed(1) + '%';

                    // 根據流量效果比例設定顏色
                    if (flowRatio > 50) {
                        flowEffectElement.style.color = '#e74c3c'; // 紅色 - 流量主導
                    } else if (flowRatio > 25) {
                        flowEffectElement.style.color = '#f39c12'; // 橙色 - 流量重要
                    } else {
                        flowEffectElement.style.color = '#8e44ad'; // 紫色 - 流量輔助
                    }
                } else {
                    flowEffectElement.textContent = '0%';
                    flowEffectElement.style.color = '#95a5a6';
                }
            }

            // 更新當前點數據
            currentPoint.compensatedHeight = compensatedHeight;
            currentPoint.isProcessed = true;
            currentPoint.speed = currentSpeed;
            currentPoint.flow = currentFlow;
            currentPoint.error = Math.abs(compensatedHeight - targetHeight);
            currentPoint.pidOutput = pidOutput; // 保存PID輸出用於顯示

            // 記錄當前PID參數
            currentPoint.kp = kp;
            currentPoint.ki = ki;
            currentPoint.kd = kd;

            // 品質評估
            if (currentPoint.error <= targetTolerance) {
                qualityPass++;
            } else {
                qualityFail++;
            }
            totalErrors.push(currentPoint.error);

            // 更新實時達標率顯示
            const currentPassRateElement = document.getElementById('current-pass-rate');
            if (currentPassRateElement) {
                const totalProcessed = qualityPass + qualityFail;
                const passRate = totalProcessed > 0 ? (qualityPass / totalProcessed) * 100 : 0;
                currentPassRateElement.textContent = passRate.toFixed(1) + '%';

                // 根據達標率設定顏色
                if (passRate >= 90) {
                    currentPassRateElement.style.color = '#27ae60'; // 綠色 - 優秀
                } else if (passRate >= 80) {
                    currentPassRateElement.style.color = '#f39c12'; // 橙色 - 良好
                } else if (passRate >= 70) {
                    currentPassRateElement.style.color = '#e67e22'; // 橙紅 - 一般
                } else {
                    currentPassRateElement.style.color = '#e74c3c'; // 紅色 - 需改善
                }
            }

            // 記錄數據用於圖表顯示
            const recordTime = timeData.length > 0 ? timeData[timeData.length - 1] + dt : 0;
            timeData.push(recordTime);
            heightData.push(compensatedHeight);
            targetData.push(targetHeight);
            speedData.push(currentSpeed);
            flowData.push(currentFlow);

            // 限制數據長度
            const maxDataPoints = timeWindow * 50;
            if (timeData.length > maxDataPoints) {
                timeData.shift();
                heightData.shift();
                targetData.shift();
                speedData.shift();
                flowData.shift();
            }

            // 更新顯示
            currentHeight = compensatedHeight;
            lastError = heightError;

            // 控制模擬速度 - 每0.1秒處理一個點
            const pointProcessingTime = 0.1; // 秒

            // 只有當時間間隔足夠時才移動到下一個點
            if (recordTime >= currentPointIndex * pointProcessingTime) {
                currentPointIndex++;

                // 如果完成所有點，停止模擬
                if (currentPointIndex >= surfaceData.length) {
                    stopSimulation();
                }
            }
        }
        
        function updateDisplay() {
            // 調試信息
            console.log(`updateDisplay: targetHeight=${targetHeight}, currentHeight=${currentHeight}, kp=${kp}`);

            document.getElementById('target-display').textContent = targetHeight.toFixed(2) + 'mm';
            document.getElementById('current-display').textContent = currentHeight.toFixed(2) + 'mm';

            // 更新即時狀態
            document.getElementById('current-position').textContent = `${currentPointIndex}/${totalPoints}`;
            document.getElementById('current-speed').textContent = currentSpeed.toFixed(1) + 'mm/s';
            document.getElementById('height-error').textContent = (targetHeight - currentHeight).toFixed(3) + 'mm';

            // 更新品質統計
            const totalProcessed = qualityPass + qualityFail;
            const qualityRate = totalProcessed > 0 ? (qualityPass / totalProcessed * 100) : 0;
            const avgError = totalErrors.length > 0 ? (totalErrors.reduce((a, b) => a + b, 0) / totalErrors.length) : 0;

            document.getElementById('quality-pass').textContent = qualityPass;
            document.getElementById('quality-fail').textContent = qualityFail;
            document.getElementById('quality-rate').textContent = qualityRate.toFixed(1) + '%';
            document.getElementById('avg-error').textContent = avgError.toFixed(3) + 'mm';
        }
        
        // drawChart函數已移除，因為響應曲線圖表已移除
        
        // drawGrid函數已移除，因為響應曲線圖表已移除
        
        // drawLine函數已移除，因為響應曲線圖表已移除
        
        // drawLegend和drawAxes函數已移除，因為響應曲線圖表已移除
        


        function drawSurfaceChart() {
            try {
                console.log('開始繪製表面圖表...');

                // 檢測並修復破圖
                if (!detectAndFixCorruptedChart()) {
                    console.error('Canvas修復失敗');
                    return false;
                }

                if (!surfaceCtx) {
                    console.error('Surface canvas context not initialized');
                    // 嘗試重新初始化
                    if (!initializeCanvas()) {
                        console.error('無法重新初始化Canvas');
                        return false;
                    }
                }

                if (!surfaceData || surfaceData.length === 0) {
                    console.warn('沒有表面數據可繪製');
                    return false;
                }

                // 驗證surfaceData的完整性
                let validDataCount = 0;
                surfaceData.forEach((point, index) => {
                    if (point && typeof point.surfaceVariation === 'number' && isFinite(point.surfaceVariation)) {
                        validDataCount++;
                    } else {
                        console.warn(`Invalid data at index ${index}:`, point);
                    }
                });

                if (validDataCount < surfaceData.length * 0.5) {
                    console.error(`Too many invalid data points: ${validDataCount}/${surfaceData.length}`);
                    // 重新生成數據
                    generateSurfaceData();
                    return false;
                }

                if (surfaceData.length === 0) {
                    console.warn('No surface data to draw');
                    // 清空畫布並顯示提示
                    surfaceCtx.clearRect(0, 0, chartWidth, chartHeight);
                    surfaceCtx.fillStyle = '#f8f9fa';
                    surfaceCtx.fillRect(0, 0, chartWidth, chartHeight);

                    surfaceCtx.fillStyle = '#6c757d';
                    surfaceCtx.font = '16px Arial';
                    surfaceCtx.textAlign = 'center';
                    surfaceCtx.fillText('請點擊"生成表面數據"', chartWidth / 2, chartHeight / 2);
                    return;
                }

                // 安全清空畫布
                try {
                    surfaceCtx.clearRect(0, 0, chartWidth, chartHeight);
                } catch (error) {
                    console.error('清空畫布失敗:', error);
                    // 重新初始化Canvas
                    if (!initializeCanvas()) {
                        return false;
                    }
                    surfaceCtx.clearRect(0, 0, chartWidth, chartHeight);
                }

                // 繪製背景
                surfaceCtx.fillStyle = '#f8f9fa';
                surfaceCtx.fillRect(0, 0, chartWidth, chartHeight);

                // 繪製網格
                drawSurfaceGrid();

                // 繪製表面數據
                drawSurfaceData();

                // 繪製軸標籤
                drawSurfaceAxes();

                // 繪製選擇框（如果正在選擇）
                if (isSelecting) {
                    drawSelectionBox();
                }

                return true; // 成功繪製

            } catch (error) {
                console.error('Error in drawSurfaceChart:', error);
                // 嘗試重新初始化canvas
                setTimeout(() => {
                    try {
                        initializeCanvas();
                        drawSurfaceChart();
                    } catch (retryError) {
                        console.error('Failed to recover surface chart:', retryError);
                    }
                }, 100);
                return false; // 繪製失敗
            }
        }

        function drawSurfaceGrid() {
            surfaceCtx.strokeStyle = '#dee2e6';
            surfaceCtx.lineWidth = 1;

            // 垂直網格線 (位置)
            for (let i = 0; i <= 10; i++) {
                const x = padding + (i / 10) * (chartWidth - 2 * padding);
                surfaceCtx.beginPath();
                surfaceCtx.moveTo(x, padding);
                surfaceCtx.lineTo(x, chartHeight - padding);
                surfaceCtx.stroke();
            }

            // 動態計算Y軸範圍
            const yAxisRange = calculateDynamicYRange();
            const { minY, maxY, stepSize } = yAxisRange;

            // 水平網格線 (動態範圍)
            const heightRange = maxY - minY;
            for (let h = minY; h <= maxY; h += stepSize) {
                const y = chartHeight - padding - ((h - minY) / heightRange) * (chartHeight - 2 * padding);
                surfaceCtx.beginPath();
                surfaceCtx.moveTo(padding, y);
                surfaceCtx.lineTo(chartWidth - padding, y);
                surfaceCtx.stroke();
            }
        }

        function calculateDynamicYRange() {
            try {
                // 收集需要顯示的數據範圍 (考慮縮放)
                let allValues = [];

                // 確定數據範圍
                const startIdx = isZoomed ? zoomRange.startIndex : 0;
                const endIdx = isZoomed ? zoomRange.endIndex : (surfaceData ? surfaceData.length - 1 : 99);

                // 檢查surfaceData是否存在且有效
                if (!surfaceData || !Array.isArray(surfaceData)) {
                    console.warn('Invalid surface data in calculateDynamicYRange');
                    return {
                        minY: targetHeight - targetTolerance - 0.1,
                        maxY: targetHeight + targetTolerance + 0.1,
                        stepSize: 0.05
                    };
                }

                // 只添加縮放範圍內的原始表面高度數據
                for (let i = startIdx; i <= endIdx && i < surfaceData.length; i++) {
                    const point = surfaceData[i];
                    if (point && typeof point.surfaceVariation === 'number' && isFinite(point.surfaceVariation)) {
                        const surfaceAbsoluteHeight = 1.0 + point.surfaceVariation; // 基準1.0mm + 變化
                        // 驗證數據合理性
                        if (isFinite(surfaceAbsoluteHeight) && surfaceAbsoluteHeight > 0 && surfaceAbsoluteHeight < 10) {
                            allValues.push(surfaceAbsoluteHeight);
                        }
                    }
                }

            // 只添加縮放範圍內的已處理塗膠絕對高度數據
            for (let i = startIdx; i <= endIdx && i < surfaceData.length; i++) {
                const point = surfaceData[i];
                if (point && point.isProcessed && point.compensatedHeight !== null &&
                    typeof point.compensatedHeight === 'number' && isFinite(point.compensatedHeight)) {
                    // 驗證數據合理性
                    if (point.compensatedHeight > 0 && point.compensatedHeight < 10) {
                        allValues.push(point.compensatedHeight);
                    }
                }
            }

            // 如果沒有數據，使用默認範圍 (包含目標高度和公差)
            if (allValues.length === 0) {
                return {
                    minY: targetHeight - targetTolerance - 0.1,
                    maxY: targetHeight + targetTolerance + 0.1,
                    stepSize: 0.05
                };
            }

            // 計算數據範圍
            const dataMin = Math.min(...allValues);
            const dataMax = Math.max(...allValues);

            // 添加邊距 (10%)
            const margin = Math.max(0.05, (dataMax - dataMin) * 0.1);
            let minY = dataMin - margin;
            let maxY = dataMax + margin;

            // 確保包含目標高度的公差範圍 (1.2mm ± 0.05mm = 1.15mm 到 1.25mm)
            minY = Math.min(minY, targetHeight - targetTolerance - 0.02);
            maxY = Math.max(maxY, targetHeight + targetTolerance + 0.02);

            // 確保最小範圍
            const minRange = 0.3;
            if (maxY - minY < minRange) {
                const center = (maxY + minY) / 2;
                minY = center - minRange / 2;
                maxY = center + minRange / 2;
            }

            // 計算合適的步長，確保包含整數刻度
            const range = maxY - minY;
            let stepSize;
            if (range <= 0.3) {
                stepSize = 0.05; // 0.05mm間隔，包含1.00, 1.05, 1.10等
            } else if (range <= 0.6) {
                stepSize = 0.1;  // 0.1mm間隔，包含1.0, 1.1, 1.2等
            } else if (range <= 1.0) {
                stepSize = 0.1;  // 改為0.1而不是0.2，確保更多整數刻度
            } else {
                stepSize = 0.2;  // 改為0.2而不是0.5，確保更密集的刻度
            }

            // 調整邊界到步長的倍數，確保包含關鍵整數點
            minY = Math.floor(minY / stepSize) * stepSize;
            maxY = Math.ceil(maxY / stepSize) * stepSize;

            // 確保包含1.0mm和1.2mm這些關鍵點
            if (minY > 1.0) minY = Math.floor(1.0 / stepSize) * stepSize;
            if (maxY < 1.2) maxY = Math.ceil(1.2 / stepSize) * stepSize;

            return { minY, maxY, stepSize };

            } catch (error) {
                console.error('Error in calculateDynamicYRange:', error);
                // 返回安全的默認範圍
                return {
                    minY: targetHeight - targetTolerance - 0.1,
                    maxY: targetHeight + targetTolerance + 0.1,
                    stepSize: 0.05
                };
            }
        }

        function drawSurfaceData() {
            try {
                if (!surfaceData || surfaceData.length < 2) {
                    console.warn('Insufficient surface data for drawing');
                    return;
                }

                const chartArea = chartWidth - 2 * padding;

                // 使用動態Y軸範圍
                const yAxisRange = calculateDynamicYRange();
                if (!yAxisRange || typeof yAxisRange.minY === 'undefined' || typeof yAxisRange.maxY === 'undefined') {
                    console.error('Invalid Y axis range:', yAxisRange);
                    return;
                }

                const { minY, maxY } = yAxisRange;
                const heightRange = maxY - minY;

                if (heightRange <= 0 || !isFinite(heightRange)) {
                    console.error('Invalid height range:', heightRange, 'minY:', minY, 'maxY:', maxY);
                    return;
                }

                // 驗證Y軸範圍的合理性
                if (!isFinite(minY) || !isFinite(maxY) || minY >= maxY) {
                    console.error('Invalid Y axis values:', { minY, maxY });
                    return;
                }

            // 調試：檢查實際數據範圍
            const surfaceHeights = surfaceData.map(p => 1.0 + p.surfaceVariation);
            const surfaceMin = Math.min(...surfaceHeights);
            const surfaceMax = Math.max(...surfaceHeights);
            console.log(`原始表面高度範圍: ${surfaceMin.toFixed(3)} - ${surfaceMax.toFixed(3)}mm`);
            console.log(`動態Y軸範圍: ${minY.toFixed(3)} - ${maxY.toFixed(3)}mm`);
            console.log(`目標高度: ${targetHeight}mm, 公差: ±${targetTolerance}mm`);

            // 繪製原始表面線 - 支持縮放範圍
            surfaceCtx.strokeStyle = '#3498db';
            surfaceCtx.lineWidth = 2;
            surfaceCtx.beginPath();

            let validPointsDrawn = 0;
            const startIdx = isZoomed ? zoomRange.startIndex : 0;
            const endIdx = isZoomed ? zoomRange.endIndex : surfaceData.length - 1;
            const displayRange = endIdx - startIdx;

            for (let i = startIdx; i <= endIdx; i++) {
                const point = surfaceData[i];
                if (!point || typeof point.surfaceVariation !== 'number') {
                    console.warn(`Invalid point at index ${i}:`, point);
                    continue;
                }

                // 計算在縮放範圍內的相對位置
                const relativeIndex = i - startIdx;
                const x = padding + (relativeIndex / displayRange) * chartArea;

                // 原始表面絕對高度 = 基準高度1.0mm + 表面變化
                const surfaceAbsoluteHeight = 1.0 + point.surfaceVariation;

                // 驗證計算結果
                if (!isFinite(surfaceAbsoluteHeight)) {
                    console.warn(`Invalid surface height at index ${i}:`, surfaceAbsoluteHeight);
                    continue;
                }

                const y = chartHeight - padding - ((surfaceAbsoluteHeight - minY) / heightRange) * (chartHeight - 2 * padding);

                // 驗證座標
                if (!isFinite(x) || !isFinite(y)) {
                    console.warn(`Invalid coordinates at index ${i}: x=${x}, y=${y}`);
                    continue;
                }

                if (validPointsDrawn === 0) {
                    surfaceCtx.moveTo(x, y);
                } else {
                    surfaceCtx.lineTo(x, y);
                }
                validPointsDrawn++;
            }

            if (validPointsDrawn > 0) {
                surfaceCtx.stroke();
            } else {
                console.error('No valid points to draw for surface line');
            }

            // 繪製塗膠後的高度曲線 (動態顯示) - 支持縮放範圍
            const processedPointsInRange = [];
            for (let i = startIdx; i <= endIdx; i++) {
                const point = surfaceData[i];
                if (point && point.isProcessed && point.compensatedHeight !== null) {
                    processedPointsInRange.push({ point, originalIndex: i });
                }
            }

            if (processedPointsInRange.length > 1) {
                surfaceCtx.strokeStyle = '#27ae60';
                surfaceCtx.lineWidth = 3;
                surfaceCtx.beginPath();

                processedPointsInRange.forEach((item, index) => {
                    const relativeIndex = item.originalIndex - startIdx;
                    const x = padding + (relativeIndex / displayRange) * chartArea;

                    // 直接使用塗膠後的絕對高度
                    const y = chartHeight - padding - ((item.point.compensatedHeight - minY) / heightRange) * (chartHeight - 2 * padding);

                    if (index === 0) {
                        surfaceCtx.moveTo(x, y);
                    } else {
                        surfaceCtx.lineTo(x, y);
                    }
                });
                surfaceCtx.stroke();
            }

            // 繪製已處理點的品質標示 - 支持縮放範圍
            for (let i = startIdx; i <= endIdx; i++) {
                const point = surfaceData[i];
                if (point && point.isProcessed && point.compensatedHeight !== null) {
                    const relativeIndex = i - startIdx;
                    const x = padding + (relativeIndex / displayRange) * chartArea;
                    const y = chartHeight - padding - ((point.compensatedHeight - minY) / heightRange) * (chartHeight - 2 * padding);

                    // 根據品質設定顏色 (檢查是否在1.2mm ± 0.05mm範圍內)
                    const isWithinTolerance = (point.compensatedHeight >= targetHeight - targetTolerance) &&
                                            (point.compensatedHeight <= targetHeight + targetTolerance);
                    surfaceCtx.fillStyle = isWithinTolerance ? '#27ae60' : '#e74c3c';

                    surfaceCtx.beginPath();
                    surfaceCtx.arc(x, y, 4, 0, 2 * Math.PI);
                    surfaceCtx.fill();

                    // 添加白色邊框
                    surfaceCtx.strokeStyle = '#ffffff';
                    surfaceCtx.lineWidth = 1;
                    surfaceCtx.stroke();
                }
            }

            // 繪製目標高度基準線 (1.2mm)
            const targetY = chartHeight - padding - ((targetHeight - minY) / heightRange) * (chartHeight - 2 * padding);
            surfaceCtx.strokeStyle = '#e74c3c';
            surfaceCtx.lineWidth = 2;
            surfaceCtx.setLineDash([5, 5]);
            surfaceCtx.beginPath();
            surfaceCtx.moveTo(padding, targetY);
            surfaceCtx.lineTo(chartWidth - padding, targetY);
            surfaceCtx.stroke();
            surfaceCtx.setLineDash([]);

            // 繪製目標公差範圍 (1.2mm ± 0.05mm = 1.15mm 到 1.25mm)
            const toleranceUpper = chartHeight - padding - ((targetHeight + targetTolerance - minY) / heightRange) * (chartHeight - 2 * padding);
            const toleranceLower = chartHeight - padding - ((targetHeight - targetTolerance - minY) / heightRange) * (chartHeight - 2 * padding);

            // 確保公差範圍在圖表範圍內
            if (toleranceUpper >= padding && toleranceLower <= chartHeight - padding) {
                surfaceCtx.fillStyle = 'rgba(39, 174, 96, 0.1)';
                surfaceCtx.fillRect(padding, toleranceUpper, chartWidth - 2 * padding, toleranceLower - toleranceUpper);

                // 繪製公差邊界線
                surfaceCtx.strokeStyle = '#27ae60';
                surfaceCtx.lineWidth = 1;
                surfaceCtx.setLineDash([3, 3]);

                // 上邊界
                if (toleranceUpper >= padding) {
                    surfaceCtx.beginPath();
                    surfaceCtx.moveTo(padding, toleranceUpper);
                    surfaceCtx.lineTo(chartWidth - padding, toleranceUpper);
                    surfaceCtx.stroke();
                }

                // 下邊界
                if (toleranceLower <= chartHeight - padding) {
                    surfaceCtx.beginPath();
                    surfaceCtx.moveTo(padding, toleranceLower);
                    surfaceCtx.lineTo(chartWidth - padding, toleranceLower);
                    surfaceCtx.stroke();
                }

                surfaceCtx.setLineDash([]);
            }

            // 繪製當前位置指示器和流量控制程度
            if (currentPointIndex < surfaceData.length) {
                const currentX = padding + (currentPointIndex / (surfaceData.length - 1)) * chartArea;

                // 當前位置線
                surfaceCtx.strokeStyle = '#f39c12';
                surfaceCtx.lineWidth = 3;
                surfaceCtx.beginPath();
                surfaceCtx.moveTo(currentX, padding);
                surfaceCtx.lineTo(currentX, chartHeight - padding);
                surfaceCtx.stroke();

                // 流量控制程度指示器
                if (isRunning && currentFlow > 0) {
                    const indicatorY = padding + 20;
                    const indicatorWidth = 60;
                    const indicatorHeight = 15;
                    const indicatorX = Math.min(currentX - indicatorWidth/2, chartWidth - padding - indicatorWidth);

                    // 背景框
                    surfaceCtx.fillStyle = 'rgba(0, 0, 0, 0.8)';
                    surfaceCtx.fillRect(indicatorX, indicatorY, indicatorWidth, indicatorHeight);

                    // 流量條
                    const maxFlow = Math.max(flowHigh, flowMedium, flowLow);
                    const flowRatio = currentFlow / maxFlow;
                    const flowBarWidth = (indicatorWidth - 4) * flowRatio;

                    // 根據流量大小設定顏色
                    let flowColor = '#27ae60'; // 小流量 - 綠色
                    if (currentFlow >= flowHigh * 0.8) {
                        flowColor = '#e74c3c'; // 大流量 - 紅色
                    } else if (currentFlow >= flowMedium * 0.8) {
                        flowColor = '#f39c12'; // 中流量 - 橙色
                    }

                    surfaceCtx.fillStyle = flowColor;
                    surfaceCtx.fillRect(indicatorX + 2, indicatorY + 2, flowBarWidth, indicatorHeight - 4);

                    // 流量文字
                    surfaceCtx.fillStyle = '#ffffff';
                    surfaceCtx.font = '10px Arial';
                    surfaceCtx.textAlign = 'center';
                    surfaceCtx.fillText(currentFlow.toFixed(1), indicatorX + indicatorWidth/2, indicatorY + indicatorHeight - 3);
                }

                // 速度指示器
                if (isRunning) {
                    const speedIndicatorY = padding + 40;
                    const speedIndicatorWidth = 50;
                    const speedIndicatorHeight = 12;
                    const speedIndicatorX = Math.min(currentX - speedIndicatorWidth/2, chartWidth - padding - speedIndicatorWidth);

                    // 背景框
                    surfaceCtx.fillStyle = 'rgba(0, 0, 0, 0.7)';
                    surfaceCtx.fillRect(speedIndicatorX, speedIndicatorY, speedIndicatorWidth, speedIndicatorHeight);

                    // 速度文字
                    surfaceCtx.fillStyle = '#ffffff';
                    surfaceCtx.font = '9px Arial';
                    surfaceCtx.textAlign = 'center';
                    surfaceCtx.fillText(currentSpeed.toFixed(0) + 'mm/s', speedIndicatorX + speedIndicatorWidth/2, speedIndicatorY + speedIndicatorHeight - 2);
                }
            }

            } catch (error) {
                console.error('Error in drawSurfaceData:', error);
                // 繪製錯誤提示
                surfaceCtx.fillStyle = '#e74c3c';
                surfaceCtx.font = '14px Arial';
                surfaceCtx.textAlign = 'center';
                surfaceCtx.fillText('圖表繪製錯誤', chartWidth / 2, chartHeight / 2);
            }
        }

        function drawSurfaceAxes() {
            surfaceCtx.fillStyle = '#2c3e50';
            surfaceCtx.font = '12px Arial';
            surfaceCtx.textAlign = 'center';

            // X軸標籤 (位置) - 調整位置避免與刻度線重疊，支持縮放範圍
            for (let i = 0; i <= 10; i++) {
                const x = padding + (i / 10) * (chartWidth - 2 * padding);

                // 計算實際位置（考慮縮放範圍）
                const startIdx = isZoomed ? zoomRange.startIndex : 0;
                const endIdx = isZoomed ? zoomRange.endIndex : totalPoints - 1;
                const actualPosition = startIdx + (i / 10) * (endIdx - startIdx);
                const positionMm = (actualPosition * 100 / totalPoints).toFixed(1); // 轉換為mm

                surfaceCtx.fillText(positionMm + 'mm', x, chartHeight - 25);
            }

            // Y軸標籤 (絕對高度) - 調整位置避免與刻度線重疊
            const yAxisRange = calculateDynamicYRange();
            const { minY, maxY, stepSize } = yAxisRange;
            const heightRange = maxY - minY;

            surfaceCtx.textAlign = 'right';
            for (let h = minY; h <= maxY; h += stepSize) {
                const y = chartHeight - padding - ((h - minY) / heightRange) * (chartHeight - 2 * padding);
                surfaceCtx.fillText(h.toFixed(2) + 'mm', padding - 35, y + 4); // 從-25改為-35，進一步向左移動
            }

            // 軸標題 - 調整位置
            surfaceCtx.textAlign = 'center';
            surfaceCtx.font = 'bold 14px Arial';
            surfaceCtx.fillText('位置 (mm)', chartWidth / 2, chartHeight - 8); // 從-5改為-8

            surfaceCtx.save();
            surfaceCtx.translate(8, chartHeight / 2); // 從15改為8，進一步向右移動避免重疊
            surfaceCtx.rotate(-Math.PI / 2);
            surfaceCtx.fillText('絕對高度 (mm)', 0, 0);
            surfaceCtx.restore();

            // 更新HTML圖例
            updateSurfaceLegend();
        }

        // 更新表面數據表格的函數
        function updateSurfaceDataTable() {
            const tbody = document.getElementById('surface-data-tbody');
            if (!tbody || !surfaceData || surfaceData.length === 0) {
                if (tbody) {
                    tbody.innerHTML = `
                        <tr>
                            <td colspan="11" style="padding: 20px; text-align: center; color: #6c757d;">
                                請先生成表面數據
                            </td>
                        </tr>
                    `;
                }
                return;
            }

            let tableHTML = '';

            surfaceData.forEach((point, index) => {
                const pointNo = index + 1;
                const xCoord = point.x.toFixed(1);
                const yCoord = 0; // Y座標固定為0，因為是直線路徑
                const preHeight = (1.0 + point.surfaceVariation).toFixed(3);

                let postHeight = '-';
                let flowRate = '-';
                let speed = '-';
                let pidKp = '-';
                let pidKi = '-';
                let pidKd = '-';
                let qualityStatus = '-';
                let rowStyle = '';

                if (point.isProcessed && point.compensatedHeight !== null) {
                    postHeight = point.compensatedHeight.toFixed(3);
                    flowRate = point.flow ? point.flow.toFixed(1) : '-';
                    speed = point.speed ? point.speed.toFixed(0) : '-';

                    // 顯示PID參數 - 統一使用3位小數
                    pidKp = point.kp ? point.kp.toFixed(3) : '-';
                    pidKi = point.ki ? point.ki.toFixed(3) : '-';
                    pidKd = point.kd ? point.kd.toFixed(3) : '-';

                    // 判斷品質狀態
                    const isWithinTolerance = (point.compensatedHeight >= targetHeight - targetTolerance) &&
                                            (point.compensatedHeight <= targetHeight + targetTolerance);

                    if (isWithinTolerance) {
                        qualityStatus = '<span style="color: #27ae60; font-weight: bold;">✓ 合格</span>';
                        rowStyle = 'background-color: #f8fff8;';
                    } else {
                        qualityStatus = '<span style="color: #e74c3c; font-weight: bold;">✗ 超差</span>';
                        rowStyle = 'background-color: #fff8f8;';
                    }
                } else if (currentPointIndex > index) {
                    // 已經處理過但沒有數據的點
                    qualityStatus = '<span style="color: #6c757d;">處理中</span>';
                    rowStyle = 'background-color: #f8f9fa;';
                } else if (currentPointIndex === index) {
                    // 當前正在處理的點
                    qualityStatus = '<span style="color: #f39c12; font-weight: bold;">→ 處理中</span>';
                    rowStyle = 'background-color: #fff9e6;';
                }

                tableHTML += `
                    <tr style="${rowStyle}">
                        <td style="padding: 4px; border: 1px solid #dee2e6; text-align: center; font-size: 11px;">${pointNo}</td>
                        <td style="padding: 4px; border: 1px solid #dee2e6; text-align: center; font-size: 11px;">${xCoord}</td>
                        <td style="padding: 4px; border: 1px solid #dee2e6; text-align: center; font-size: 11px;">${yCoord}</td>
                        <td style="padding: 4px; border: 1px solid #dee2e6; text-align: center; font-size: 11px;">${preHeight}</td>
                        <td style="padding: 4px; border: 1px solid #dee2e6; text-align: center; font-size: 11px;">${postHeight}</td>
                        <td style="padding: 4px; border: 1px solid #dee2e6; text-align: center; font-size: 11px;">${flowRate}</td>
                        <td style="padding: 4px; border: 1px solid #dee2e6; text-align: center; font-size: 11px;">${speed}</td>
                        <td style="padding: 4px; border: 1px solid #dee2e6; text-align: center; font-size: 11px;">${pidKp}</td>
                        <td style="padding: 4px; border: 1px solid #dee2e6; text-align: center; font-size: 11px;">${pidKi}</td>
                        <td style="padding: 4px; border: 1px solid #dee2e6; text-align: center; font-size: 11px;">${pidKd}</td>
                        <td style="padding: 4px; border: 1px solid #dee2e6; text-align: center; font-size: 11px;">${qualityStatus}</td>
                    </tr>
                `;
            });

            tbody.innerHTML = tableHTML;

            // 自動滾動到當前處理的點 (可選)
            const autoScrollCheckbox = document.getElementById('auto-scroll-checkbox');
            if (autoScrollCheckbox && autoScrollCheckbox.checked && currentPointIndex < surfaceData.length) {
                const currentRow = tbody.children[currentPointIndex];
                const tableContainer = document.querySelector('#surface-data-table').parentElement;

                if (currentRow && tableContainer) {
                    // 計算當前行在表格容器中的位置
                    const rowTop = currentRow.offsetTop;
                    const containerHeight = tableContainer.clientHeight;
                    const containerScrollTop = tableContainer.scrollTop;

                    // 只有當行不在可視區域時才滾動，且使用平滑滾動
                    if (rowTop < containerScrollTop || rowTop > containerScrollTop + containerHeight - 50) {
                        // 使用平滑滾動，避免突然跳動
                        tableContainer.scrollTo({
                            top: rowTop - containerHeight / 2,
                            behavior: 'smooth'
                        });
                    }
                }
            }
        }

        // 更新表面圖例的HTML函數
        function updateSurfaceLegend() {
            const legendContainer = document.getElementById('surface-legend');
            if (!legendContainer) return;

            const legendItems = [
                { color: '#3498db', label: '原始表面', type: 'line' },
                { color: '#27ae60', label: '塗膠曲線', type: 'line' },
                { color: '#e74c3c', label: '目標1.2mm', type: 'dash' },
                { color: '#27ae60', label: '合格點', type: 'circle' },
                { color: '#e74c3c', label: '超差點', type: 'circle' },
                { color: '#f39c12', label: '當前位置+流量', type: 'line' }
            ];

            let legendHTML = '<div style="display: flex; justify-content: space-around; align-items: center; flex-wrap: wrap; gap: 10px;">';

            legendItems.forEach(item => {
                let symbolHTML = '';

                if (item.type === 'line') {
                    symbolHTML = `<div style="width: 20px; height: 2px; background-color: ${item.color}; margin-right: 5px;"></div>`;
                } else if (item.type === 'dash') {
                    symbolHTML = `<div style="width: 20px; height: 2px; background: repeating-linear-gradient(to right, ${item.color} 0px, ${item.color} 3px, transparent 3px, transparent 6px); margin-right: 5px;"></div>`;
                } else if (item.type === 'circle') {
                    symbolHTML = `<div style="width: 8px; height: 8px; background-color: ${item.color}; border-radius: 50%; border: 1px solid white; margin-right: 5px;"></div>`;
                }

                legendHTML += `
                    <div style="display: flex; align-items: center; font-size: 11px; color: #2c3e50;">
                        ${symbolHTML}
                        <span>${item.label}</span>
                    </div>
                `;
            });

            legendHTML += '</div>';
            legendContainer.innerHTML = legendHTML;
        }

        // 檢測破圖並修復
        function detectAndFixCorruptedChart() {
            try {
                if (!surfaceCtx || !surfaceCanvas) {
                    console.warn('Canvas不存在，重新初始化...');
                    return initializeCanvas();
                }

                // 檢查Canvas狀態
                const canvasState = surfaceCtx.getImageData(0, 0, 1, 1);
                if (!canvasState) {
                    console.warn('Canvas狀態異常，重新初始化...');
                    return initializeCanvas();
                }

                // 檢查是否有異常的繪製狀態
                try {
                    surfaceCtx.save();
                    surfaceCtx.restore();
                } catch (error) {
                    console.error('Canvas狀態損壞:', error);
                    return initializeCanvas();
                }

                return true;

            } catch (error) {
                console.error('破圖檢測失敗:', error);
                return initializeCanvas();
            }
        }

        // 檢測空白圖表並修復
        function detectAndFixBlankChart() {
            try {
                if (!surfaceCtx || !surfaceCanvas) {
                    console.warn('Canvas不存在，重新初始化...');
                    return initializeCanvas();
                }

                // 檢查Canvas是否為空白
                const imageData = surfaceCtx.getImageData(0, 0, surfaceCanvas.width, surfaceCanvas.height);
                const data = imageData.data;
                let hasContent = false;

                // 檢查是否有非透明像素
                for (let i = 3; i < data.length; i += 4) {
                    if (data[i] > 0) { // alpha通道
                        hasContent = true;
                        break;
                    }
                }

                if (!hasContent) {
                    console.warn('檢測到空白圖表，嘗試修復...');

                    // 確保有數據
                    if (!surfaceData || surfaceData.length === 0) {
                        generateSurfaceData();
                    }

                    // 重新繪製
                    setTimeout(() => {
                        drawSurfaceChart();
                        updateSurfaceDataTable();
                    }, 100);

                    return false;
                }

                return true;

            } catch (error) {
                console.error('空白圖表檢測失敗:', error);
                return false;
            }
        }

        // 參數記憶功能
        function saveParameters() {
            const parameters = {
                kp: kp,
                ki: ki,
                kd: kd,
                waveAmplitude: waveAmplitude,
                waveFrequency: waveFrequency,
                surfaceNoise: surfaceNoise,
                baseSpeed: baseSpeed,
                speedRange: speedRange,
                targetTolerance: targetTolerance,
                flowControlMode: flowControlMode,
                flowHigh: flowHigh,
                flowMedium: flowMedium,
                flowLow: flowLow,
                manualFlowSelection: manualFlowSelection,
                maxSmartIterations: maxSmartIterations,
                targetPassRate: targetPassRate,
                systemLoad: systemLoad,
                systemDelay: systemDelay,
                useIntelligentAlgorithm: useIntelligentAlgorithm,
                useFixedSeed: useFixedSeed,
                enableAnimation: enableAnimation,
                scanTime: scanTime
            };

            try {
                localStorage.setItem('pidGlueControlParameters', JSON.stringify(parameters));
                console.log('參數已保存到本地存儲');
            } catch (error) {
                console.warn('無法保存參數到本地存儲:', error);
            }
        }

        function loadParameters() {
            try {
                const saved = localStorage.getItem('pidGlueControlParameters');
                if (saved) {
                    const parameters = JSON.parse(saved);

                    // 載入PID參數
                    if (parameters.kp !== undefined) kp = parameters.kp;
                    if (parameters.ki !== undefined) ki = parameters.ki;
                    if (parameters.kd !== undefined) kd = parameters.kd;

                    // 載入表面參數
                    if (parameters.waveAmplitude !== undefined) waveAmplitude = parameters.waveAmplitude;
                    if (parameters.waveFrequency !== undefined) waveFrequency = parameters.waveFrequency;
                    if (parameters.surfaceNoise !== undefined) surfaceNoise = parameters.surfaceNoise;

                    // 載入移動控制參數
                    if (parameters.baseSpeed !== undefined) baseSpeed = parameters.baseSpeed;
                    if (parameters.speedRange !== undefined) speedRange = parameters.speedRange;
                    if (parameters.targetTolerance !== undefined) targetTolerance = parameters.targetTolerance;

                    // 載入流量控制參數
                    if (parameters.flowControlMode !== undefined) flowControlMode = parameters.flowControlMode;
                    if (parameters.flowHigh !== undefined) flowHigh = parameters.flowHigh;
                    if (parameters.flowMedium !== undefined) flowMedium = parameters.flowMedium;
                    if (parameters.flowLow !== undefined) flowLow = parameters.flowLow;
                    if (parameters.manualFlowSelection !== undefined) manualFlowSelection = parameters.manualFlowSelection;
                    if (parameters.maxSmartIterations !== undefined) maxSmartIterations = parameters.maxSmartIterations;
                    if (parameters.targetPassRate !== undefined) targetPassRate = parameters.targetPassRate;
                    if (parameters.systemLoad !== undefined) systemLoad = parameters.systemLoad;
                    if (parameters.systemDelay !== undefined) systemDelay = parameters.systemDelay;
                    if (parameters.useIntelligentAlgorithm !== undefined) useIntelligentAlgorithm = parameters.useIntelligentAlgorithm;
                    if (parameters.useFixedSeed !== undefined) useFixedSeed = parameters.useFixedSeed;
                    if (parameters.enableAnimation !== undefined) enableAnimation = parameters.enableAnimation;
                    if (parameters.scanTime !== undefined) scanTime = parameters.scanTime;

                    console.log('參數已從本地存儲載入');
                    return true;
                }
            } catch (error) {
                console.warn('無法從本地存儲載入參數:', error);
            }
            return false;
        }

        function updateUIFromParameters() {
            // 更新PID參數UI
            const kpSlider = document.getElementById('kp-slider');
            const kiSlider = document.getElementById('ki-slider');
            const kdSlider = document.getElementById('kd-slider');
            const kpValue = document.getElementById('kp-value');
            const kiValue = document.getElementById('ki-value');
            const kdValue = document.getElementById('kd-value');

            if (kpSlider) kpSlider.value = kp;
            if (kiSlider) kiSlider.value = ki;
            if (kdSlider) kdSlider.value = kd;
            if (kpValue) kpValue.textContent = kp.toFixed(3);
            if (kiValue) kiValue.textContent = ki.toFixed(3);
            if (kdValue) kdValue.textContent = kd.toFixed(3);

            // 更新表面參數UI
            const waveAmpSlider = document.getElementById('wave-amplitude');
            const waveFreqSlider = document.getElementById('wave-frequency');
            const surfaceNoiseSlider = document.getElementById('surface-noise');
            const waveAmpValue = document.getElementById('wave-amplitude-value');
            const waveFreqValue = document.getElementById('wave-frequency-value');
            const surfaceNoiseValue = document.getElementById('surface-noise-value');

            if (waveAmpSlider) waveAmpSlider.value = waveAmplitude;
            if (waveFreqSlider) waveFreqSlider.value = waveFrequency;
            if (surfaceNoiseSlider) surfaceNoiseSlider.value = surfaceNoise;
            if (waveAmpValue) waveAmpValue.textContent = waveAmplitude.toFixed(2) + 'mm';
            if (waveFreqValue) waveFreqValue.textContent = waveFrequency;
            if (surfaceNoiseValue) surfaceNoiseValue.textContent = surfaceNoise.toFixed(2) + 'mm';

            // 更新移動控制參數UI
            const baseSpeedSlider = document.getElementById('base-speed');
            const speedRangeSlider = document.getElementById('speed-range');
            const targetToleranceSlider = document.getElementById('target-tolerance');
            const baseSpeedValue = document.getElementById('base-speed-value');
            const speedRangeValue = document.getElementById('speed-range-value');
            const targetToleranceValue = document.getElementById('target-tolerance-value');

            if (baseSpeedSlider) baseSpeedSlider.value = baseSpeed;
            if (speedRangeSlider) speedRangeSlider.value = speedRange;
            if (targetToleranceSlider) targetToleranceSlider.value = targetTolerance;
            if (baseSpeedValue) baseSpeedValue.textContent = baseSpeed.toFixed(2) + 'mm/s';
            if (speedRangeValue) speedRangeValue.textContent = '±' + speedRange.toFixed(2) + '%';
            if (targetToleranceValue) targetToleranceValue.textContent = '±' + targetTolerance.toFixed(2) + 'mm';

            // 更新流量控制參數UI
            const flowControlModeSelect = document.getElementById('flow-control-mode');
            const flowHighSlider = document.getElementById('flow-high');
            const flowMediumSlider = document.getElementById('flow-medium');
            const flowLowSlider = document.getElementById('flow-low');
            const flowHighValue = document.getElementById('flow-high-value');
            const flowMediumValue = document.getElementById('flow-medium-value');
            const flowLowValue = document.getElementById('flow-low-value');

            if (flowControlModeSelect) flowControlModeSelect.value = flowControlMode;
            if (flowHighSlider) flowHighSlider.value = flowHigh;
            if (flowMediumSlider) flowMediumSlider.value = flowMedium;
            if (flowLowSlider) flowLowSlider.value = flowLow;
            if (flowHighValue) flowHighValue.textContent = flowHigh.toFixed(1) + 'cm³/s';
            if (flowMediumValue) flowMediumValue.textContent = flowMedium.toFixed(1) + 'cm³/s';
            if (flowLowValue) flowLowValue.textContent = flowLow.toFixed(1) + 'cm³/s';

            // 更新手動流量選擇
            const manualFlowRadio = document.getElementById(`manual-flow-${manualFlowSelection}`);
            if (manualFlowRadio) manualFlowRadio.checked = true;

            // 顯示或隱藏手動選擇選項
            const manualFlowSelectionDiv = document.getElementById('manual-flow-selection');
            if (manualFlowSelectionDiv) {
                if (flowControlMode === 'manual') {
                    manualFlowSelectionDiv.style.display = 'flex';
                } else {
                    manualFlowSelectionDiv.style.display = 'none';
                }
            }

            // 更新智能點膠設定
            const smartIterationsSlider = document.getElementById('smart-iterations');
            const smartIterationsValue = document.getElementById('smart-iterations-value');
            const targetPassRateSlider = document.getElementById('target-pass-rate');
            const targetPassRateValue = document.getElementById('target-pass-rate-value');

            if (smartIterationsSlider) smartIterationsSlider.value = maxSmartIterations;
            if (smartIterationsValue) smartIterationsValue.textContent = maxSmartIterations + '次';
            if (targetPassRateSlider) targetPassRateSlider.value = Math.round(targetPassRate * 100);
            if (targetPassRateValue) targetPassRateValue.textContent = Math.round(targetPassRate * 100) + '%';

            // 更新系統性能設定
            const systemLoadSlider = document.getElementById('system-load');
            const systemLoadValue = document.getElementById('system-load-value');
            const systemDelaySlider = document.getElementById('system-delay');
            const systemDelayValue = document.getElementById('system-delay-value');

            if (systemLoadSlider) systemLoadSlider.value = systemLoad;
            if (systemLoadValue) systemLoadValue.textContent = systemLoad.toString();
            if (systemDelaySlider) systemDelaySlider.value = systemDelay;
            if (systemDelayValue) systemDelayValue.textContent = systemDelay.toFixed(1) + '秒';

            // 更新優化模式選擇
            const intelligentModeRadio = document.getElementById('mode-intelligent');
            const pidModeRadio = document.getElementById('mode-pid');
            if (intelligentModeRadio) intelligentModeRadio.checked = useIntelligentAlgorithm;
            if (pidModeRadio) pidModeRadio.checked = !useIntelligentAlgorithm;

            // 更新固定種子開關
            const fixedSeedCheckbox = document.getElementById('use-fixed-seed');
            if (fixedSeedCheckbox) fixedSeedCheckbox.checked = useFixedSeed;

            // 更新動畫開關
            const enableAnimationCheckbox = document.getElementById('enable-animation');
            if (enableAnimationCheckbox) enableAnimationCheckbox.checked = enableAnimation;

            // 更新掃描時間設置
            const scanTimeInput = document.getElementById('scan-time-setting');
            if (scanTimeInput) scanTimeInput.value = scanTime;

            // 更新時間顯示
            updateTimeDisplay();

            console.log('UI已更新為載入的參數值');
        }

        // 系統健康檢查函數
        function checkSystemHealth() {
            const issues = [];

            // 檢查Canvas
            if (!surfaceCtx) {
                issues.push('Surface canvas context is null');
            }

            // 檢查表面數據
            if (!surfaceData || !Array.isArray(surfaceData)) {
                issues.push('Surface data is invalid');
            } else if (surfaceData.length === 0) {
                issues.push('Surface data is empty');
            } else {
                let invalidPoints = 0;
                surfaceData.forEach((point, index) => {
                    if (!point || typeof point.surfaceVariation !== 'number') {
                        invalidPoints++;
                    }
                });
                if (invalidPoints > 0) {
                    issues.push(`${invalidPoints} invalid surface data points`);
                }
            }

            // 檢查DOM元素
            const requiredElements = ['surface-chart', 'surface-data-table', 'start-btn'];
            requiredElements.forEach(id => {
                if (!document.getElementById(id)) {
                    issues.push(`Missing DOM element: ${id}`);
                }
            });

            if (issues.length > 0) {
                console.warn('系統健康檢查發現問題:', issues);
                return false;
            }

            console.log('系統健康檢查通過');
            return true;
        }

        // 強制重新繪製所有圖表的函數
        function forceRedrawAllCharts() {
            try {
                console.log('強制重新繪製所有圖表...');

                // 檢查canvas是否有效
                if (!surfaceCtx) {
                    console.warn('Canvas context invalid, reinitializing...');
                    initializeCanvas();
                }

                // 檢查數據是否有效
                if (!surfaceData || surfaceData.length === 0) {
                    console.warn('Surface data invalid, regenerating...');
                    generateSurfaceData();
                    return;
                }

                // 重新繪製
                updateDisplay();
                drawSurfaceChart();
                updateSurfaceDataTable();

                console.log('所有圖表重新繪製完成');

            } catch (error) {
                console.error('強制重繪失敗:', error);
                // 最後的嘗試：完全重新初始化
                setTimeout(() => {
                    initializeCanvas();
                    generateSurfaceData();
                }, 100);
            }
        }

        // 圖表縮放功能
        function setupChartZoom() {
            const canvas = document.getElementById('surface-chart');
            if (!canvas) return;

            // 滑鼠按下事件
            canvas.addEventListener('mousedown', (e) => {
                const rect = canvas.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;

                // 檢查是否在圖表區域內
                if (x >= padding && x <= canvas.width - padding &&
                    y >= padding && y <= canvas.height - padding) {
                    isSelecting = true;
                    selectionStart = { x, y };
                    selectionEnd = { x, y };
                    canvas.style.cursor = 'crosshair';
                }
            });

            // 滑鼠移動事件
            canvas.addEventListener('mousemove', (e) => {
                if (!isSelecting) return;

                const rect = canvas.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;

                selectionEnd = { x, y };
                drawSurfaceChart(); // 重繪圖表並顯示選擇框
            });

            // 滑鼠放開事件
            canvas.addEventListener('mouseup', (e) => {
                if (!isSelecting) return;

                isSelecting = false;
                canvas.style.cursor = 'default';

                // 計算選擇範圍
                const startX = Math.min(selectionStart.x, selectionEnd.x);
                const endX = Math.max(selectionStart.x, selectionEnd.x);

                // 轉換為數據索引
                const chartArea = canvas.width - 2 * padding;
                const startRatio = Math.max(0, (startX - padding) / chartArea);
                const endRatio = Math.min(1, (endX - padding) / chartArea);

                const currentRange = zoomRange.endIndex - zoomRange.startIndex;
                const newStartIndex = Math.floor(zoomRange.startIndex + startRatio * currentRange);
                const newEndIndex = Math.ceil(zoomRange.startIndex + endRatio * currentRange);

                // 確保選擇範圍有效
                if (newEndIndex - newStartIndex >= 5) { // 至少5個點
                    zoomRange.startIndex = newStartIndex;
                    zoomRange.endIndex = newEndIndex;
                    isZoomed = true;

                    // 顯示重置按鈕
                    const resetBtn = document.getElementById('reset-zoom');
                    if (resetBtn) resetBtn.style.display = 'inline-block';

                    drawSurfaceChart(); // 重繪縮放後的圖表
                }
            });

            // 滑鼠離開事件
            canvas.addEventListener('mouseleave', () => {
                if (isSelecting) {
                    isSelecting = false;
                    canvas.style.cursor = 'default';
                    drawSurfaceChart(); // 清除選擇框
                }
            });
        }

        // 重置縮放
        function resetZoom() {
            isZoomed = false;
            zoomRange = { startIndex: 0, endIndex: totalPoints - 1 };

            // 隱藏重置按鈕
            const resetBtn = document.getElementById('reset-zoom');
            if (resetBtn) resetBtn.style.display = 'none';

            drawSurfaceChart(); // 重繪全局圖表
        }

        // 繪製選擇框
        function drawSelectionBox() {
            if (!isSelecting) return;

            const startX = Math.min(selectionStart.x, selectionEnd.x);
            const startY = Math.min(selectionStart.y, selectionEnd.y);
            const width = Math.abs(selectionEnd.x - selectionStart.x);
            const height = Math.abs(selectionEnd.y - selectionStart.y);

            // 繪製半透明的選擇框
            surfaceCtx.fillStyle = 'rgba(52, 152, 219, 0.2)';
            surfaceCtx.fillRect(startX, startY, width, height);

            // 繪製選擇框邊框
            surfaceCtx.strokeStyle = '#3498db';
            surfaceCtx.lineWidth = 2;
            surfaceCtx.setLineDash([5, 5]);
            surfaceCtx.strokeRect(startX, startY, width, height);
            surfaceCtx.setLineDash([]); // 重置線條樣式
        }

    </script>
</body>
</html>
