<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BLU邊緣塗膠PID控制模擬系統</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft JhengHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50, #3498db);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            padding: 20px;
        }

        .control-panel {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .visualization-panel {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .section-title {
            font-size: 1.5em;
            color: #2c3e50;
            margin-bottom: 15px;
            border-bottom: 3px solid #3498db;
            padding-bottom: 5px;
        }

        .parameter-group {
            margin-bottom: 20px;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }

        .parameter-group h3 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 1.2em;
        }

        .parameter-row {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }

        .parameter-row label {
            flex: 1;
            color: #555;
            font-weight: 500;
        }

        .parameter-row input {
            flex: 1;
            padding: 8px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .parameter-row input:focus {
            outline: none;
            border-color: #3498db;
        }

        .parameter-row .value-display {
            flex: 0.5;
            text-align: right;
            font-weight: bold;
            color: #2c3e50;
        }

        .button-group {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60, #229954);
            color: white;
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(39, 174, 96, 0.4);
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            color: white;
        }

        .btn-warning:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(243, 156, 18, 0.4);
        }

        .btn-danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
        }

        .btn-danger:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(231, 76, 60, 0.4);
        }

        .canvas-container {
            position: relative;
            margin-bottom: 20px;
        }

        canvas {
            border: 2px solid #ddd;
            border-radius: 8px;
            background: white;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        }

        /* 分頁標籤樣式 */
        .tab-container {
            display: flex;
            border-bottom: 2px solid #ddd;
        }

        .tab-button {
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-bottom: none;
            padding: 8px 16px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }

        .tab-button:hover {
            background-color: #e9ecef;
        }

        .tab-button.active {
            background-color: #007bff;
            color: white;
        }

        .tab-content {
            padding: 10px 0;
        }

        /* 數據表格樣式 */
        .data-table-container {
            background-color: white;
            border-radius: 4px;
        }

        #height-data-table th {
            background-color: #f8f9fa;
            font-weight: bold;
            text-align: center;
        }

        #height-data-table td {
            text-align: center;
            padding: 4px 8px;
        }

        #height-data-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }

        #height-data-table tr:hover {
            background-color: #e3f2fd;
        }

        .status-panel {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 20px;
        }

        .status-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #27ae60;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .status-card h4 {
            color: #2c3e50;
            margin-bottom: 8px;
            font-size: 1.1em;
        }

        .status-value {
            font-size: 1.5em;
            font-weight: bold;
            color: #27ae60;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #ecf0f1;
            border-radius: 10px;
            overflow: hidden;
            margin-top: 10px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3498db, #2980b9);
            transition: width 0.3s ease;
            border-radius: 10px;
        }

        .log-panel {
            grid-column: 1 / -1;
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            max-height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }

        .log-timestamp {
            color: #95a5a6;
            margin-right: 10px;
        }

        .log-info { color: #3498db; }
        .log-success { color: #27ae60; }
        .log-warning { color: #f39c12; }
        .log-error { color: #e74c3c; }

        @media (max-width: 1200px) {
            .main-content {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .parameter-row {
                flex-direction: column;
                align-items: stretch;
            }

            .parameter-row label {
                margin-bottom: 5px;
            }

            .button-group {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 BLU邊緣塗膠PID控制模擬系統</h1>
            <p>智能表面掃描 • PID控制算法 • 精確塗膠控制</p>
        </div>

        <div class="main-content">
            <!-- 控制面板 -->
            <div class="control-panel">
                <h2 class="section-title">🎛️ 控制參數設定</h2>

                <!-- PID參數設定 -->
                <div class="parameter-group">
                    <h3>PID控制器參數</h3>
                    <div class="parameter-row">
                        <label>比例增益 (Kp):</label>
                        <input type="range" id="kp" min="0.1" max="5.0" step="0.1" value="1.2">
                        <span class="value-display" id="kp-value">1.2</span>
                    </div>
                    <div class="parameter-row">
                        <label>積分增益 (Ki):</label>
                        <input type="range" id="ki" min="0.01" max="1.0" step="0.01" value="0.1">
                        <span class="value-display" id="ki-value">0.1</span>
                    </div>
                    <div class="parameter-row">
                        <label>微分增益 (Kd):</label>
                        <input type="range" id="kd" min="0.01" max="1.0" step="0.01" value="0.05">
                        <span class="value-display" id="kd-value">0.05</span>
                    </div>
                </div>

                <!-- 補償模式設定 -->
                <div class="parameter-group">
                    <h3>補償模式設定</h3>
                    <div class="parameter-row">
                        <label>模式選擇:</label>
                        <div style="flex: 2;">
                            <input type="radio" id="comp-mode-standard" name="compensation-mode" value="standard">
                            <label for="comp-mode-standard" style="margin-right: 10px; font-weight: normal;">標準補償</label>
                            <input type="radio" id="comp-mode-pid" name="compensation-mode" value="pid" checked>
                            <label for="comp-mode-pid" style="margin-right: 10px; font-weight: normal;">PID補償</label>
                            <input type="radio" id="comp-mode-smart" name="compensation-mode" value="smart" disabled>
                            <label for="comp-mode-smart" style="font-weight: normal; color: #999;">智能點膠(開發中)</label>
                        </div>
                    </div>
                </div>

                <!-- 目標參數設定 -->
                <div class="parameter-group">
                    <h3>目標塗膠參數</h3>
                    <div class="parameter-row">
                        <label>目標高度 (mm):</label>
                        <input type="range" id="target-height" min="0.8" max="1.6" step="0.1" value="1.2">
                        <span class="value-display" id="target-height-value">1.2</span>
                    </div>
                    <div class="parameter-row">
                        <label>目標寬度 (mm):</label>
                        <input type="range" id="target-width" min="1.0" max="2.0" step="0.1" value="1.4">
                        <span class="value-display" id="target-width-value">1.4</span>
                    </div>
                    <div class="parameter-row">
                        <label>公差範圍 (±mm):</label>
                        <input type="range" id="tolerance" min="0.1" max="0.5" step="0.05" value="0.2">
                        <span class="value-display" id="tolerance-value">0.2</span>
                    </div>
                </div>

                <!-- 機器人控制參數 -->
                <div class="parameter-group">
                    <h3>機器人控制參數</h3>
                    <div class="parameter-row">
                        <label>最大速度 (mm/s):</label>
                        <input type="range" id="max-speed" min="5" max="50" step="5" value="20">
                        <span class="value-display" id="max-speed-value">20</span>
                    </div>
                    <div class="parameter-row">
                        <label>加速度 (mm/s²):</label>
                        <input type="range" id="acceleration" min="10" max="100" step="10" value="50">
                        <span class="value-display" id="acceleration-value">50</span>
                    </div>
                </div>

                <!-- 塗膠閥控制參數 -->
                <div class="parameter-group">
                    <h3>螺桿閥控制參數 (開度百分比)</h3>
                    <div class="parameter-row">
                        <label>第1段開度 (%):</label>
                        <input type="range" id="flow-stage1" min="0" max="100" step="1" value="20">
                        <span class="value-display" id="flow-stage1-value">20</span>%
                    </div>
                    <div class="parameter-row">
                        <label>第2段開度 (%):</label>
                        <input type="range" id="flow-stage2" min="0" max="100" step="1" value="50">
                        <span class="value-display" id="flow-stage2-value">50</span>%
                    </div>
                    <div class="parameter-row">
                        <label>第3段開度 (%):</label>
                        <input type="range" id="flow-stage3" min="0" max="100" step="1" value="100">
                        <span class="value-display" id="flow-stage3-value">100</span>%
                    </div>
                </div>
 
                <!-- 控制按鈕 -->
                <div class="button-group">
                    <button class="btn btn-primary" id="scan-btn">🔍 開始掃描</button>
                    <button class="btn btn-success" id="start-btn" disabled>▶️ 開始塗膠</button>
                    <button class="btn btn-warning" id="pause-btn" disabled>⏸️ 暫停</button>
                    <button class="btn btn-danger" id="stop-btn" disabled>⏹️ 停止</button>
                </div>
            </div>

            <!-- 視覺化面板 -->
            <div class="visualization-panel">
                <h2 class="section-title">📊 即時監控視覺化</h2>

                <!-- BLU表面高度圖 -->
                <div class="canvas-container">
                    <h3>BLU表面高度分佈</h3>

                    <!-- 分頁標籤 -->
                    <div class="tab-container" style="margin-bottom: 10px;">
                        <button class="tab-button active" id="height-map-tab" onclick="switchHeightTab('map')">高度分佈圖</button>
                        <button class="tab-button" id="height-data-tab" onclick="switchHeightTab('data')">點位數據表</button>
                    </div>

                    <!-- 高度分佈圖 -->
                    <div id="height-map-view" class="tab-content">
                        <canvas id="height-canvas" width="500" height="300"></canvas>
                    </div>

                    <!-- 點位數據表 -->
                    <div id="height-data-view" class="tab-content" style="display: none;">
                        <div class="data-table-container" style="max-height: 300px; overflow-y: auto; border: 1px solid #ddd;">
                            <table id="height-data-table" style="width: 100%; border-collapse: collapse; font-size: 12px;">
                                <thead style="background-color: #f5f5f5; position: sticky; top: 0;">
                                    <tr>
                                        <th style="border: 1px solid #ddd; padding: 5px;">點位</th>
                                        <th style="border: 1px solid #ddd; padding: 5px;">X座標</th>
                                        <th style="border: 1px solid #ddd; padding: 5px;">Y座標</th>
                                        <th style="border: 1px solid #ddd; padding: 5px;">原始高度(mm)</th>
                                        <th style="border: 1px solid #ddd; padding: 5px;">補償後高度(mm)</th>
                                        <th style="border: 1px solid #ddd; padding: 5px;">狀態</th>
                                    </tr>
                                </thead>
                                <tbody id="height-data-tbody">
                                    <tr>
                                        <td colspan="6" style="text-align: center; padding: 20px; color: #666;">請先進行表面掃描</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- PID控制響應圖 -->
                <div class="canvas-container">
                    <h3>PID控制響應</h3>
                    <canvas id="pid-canvas" width="500" height="200"></canvas>
                </div>

                <!-- 塗膠剖面趨勢圖 -->
                <div class="canvas-container">
                    <h3>塗膠剖面趨勢</h3>
                    <canvas id="profile-canvas" width="500" height="250"></canvas>
                </div>

                <!-- 機器人路徑圖 -->
                <div class="canvas-container">
                    <h3>機器人移動路徑</h3>
                    <canvas id="robot-canvas" width="500" height="300"></canvas>
                </div>
            </div>
        </div>

        <!-- 狀態監控面板 -->
        <div class="status-panel">
            <div class="status-card">
                <h4>🎯 當前位置</h4>
                <div class="status-value" id="current-position">待機中</div>
            </div>
            <div class="status-card">
                <h4>⚡ 機器人速度</h4>
                <div class="status-value" id="robot-speed">0 mm/s</div>
            </div>
            <div class="status-card">
                <h4>💧 塗膠流量</h4>
                <div class="status-value" id="flow-rate">0 cm³/s</div>
            </div>
            <div class="status-card">
                <h4>📏 高度偏差</h4>
                <div class="status-value" id="height-error">0 mm</div>
            </div>
            <div class="status-card">
                <h4>📈 完成進度</h4>
                <div class="status-value" id="progress-percent">0%</div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progress-fill" style="width: 0%"></div>
                </div>
            </div>
            <div class="status-card">
                <h4>🔧 PID輸出</h4>
                <div class="status-value" id="pid-output">0</div>
            </div>
        </div>

        <!-- 日誌面板 -->
        <div class="log-panel" id="log-panel">
            <div class="log-entry">
                <span class="log-timestamp">[00:00:00]</span>
                <span class="log-info">系統初始化完成，等待開始掃描...</span>
            </div>
        </div>
    </div>

    <script>
        // 全域變數
        let isScanning = false;
        let isRunning = false;
        let isPaused = false;
        let scanData = [];
        let currentPoint = 0;
        let animationId = null;
        let startTime = null;
        // 移除智能優化模式相關變數

        // 滑鼠懸停相關變數
        let mouseX = 0;
        let mouseY = 0;
        let showTooltip = false;
        let tooltipData = null;

        // 新增：流量/速度對高度影響的因子
        // 增加此因子以增強流量/速度變化對膠高的影響
        // --- 可調試的關鍵模擬參數 ---
        const FLOW_SPEED_TO_HEIGHT_FACTOR = 0.5;
        const MAX_SCREW_VALVE_FLOW_CM3_S = 1.0;
        // 增大 PID_OUTPUT_TO_HEIGHT_FACTOR，確保PID能夠有效補償到目標範圍
        const PID_OUTPUT_TO_HEIGHT_FACTOR = 1.2; // 增大到1.2以提供更強的補償力度
        const ROBOT_SPEED_ADJUSTMENT_FACTOR = 2.2;
        // 建議PID調參起始點: 增強控制力度以達到目標管制線
        let initialPidSettings = { kp: 3.0, ki: 0.1, kd: 0.05 };
        // --- END 可調試參數 ---
 
        // 補償模式相關變數
        let currentCompensationMode = 'pid'; // 'standard', 'pid'
 
        // PID控制器類別
        class PIDController {
            constructor(kp, ki, kd) { // 現在從 initialPidSettings 或 UI獲取
                this.kp = parseFloat(document.getElementById('kp').value) || initialPidSettings.kp;
                this.ki = parseFloat(document.getElementById('ki').value) || initialPidSettings.ki;
                this.kd = parseFloat(document.getElementById('kd').value) || initialPidSettings.kd;
                this.previousError = 0;
                this.integral = 0;
                this.lastTime = Date.now();
            }

            update(setpoint, currentValue) {
                const currentTime = Date.now();
                const deltaTime = (currentTime - this.lastTime) / 1000; // 轉換為秒

                const error = setpoint - currentValue;

                // 比例項
                const proportional = this.kp * error;

                // 積分項
                this.integral += error * deltaTime;
                const integral = this.ki * this.integral;

                // 微分項
                const derivative = this.kd * (error - this.previousError) / deltaTime;

                // PID輸出
                const output = proportional + integral + derivative;

                this.previousError = error;
                this.lastTime = currentTime;

                return {
                    output: output,
                    error: error,
                    proportional: proportional,
                    integral: integral,
                    derivative: derivative
                };
            }

            reset() {
                this.previousError = 0;
                this.integral = 0;
                this.lastTime = Date.now();
            }

            setParameters(kp, ki, kd) {
                this.kp = kp;
                this.ki = ki;
                this.kd = kd;
            }
        }

        // BLU表面掃描模擬器
        class BLUSurfaceScanner {
            constructor() {
                this.points = 100; // 掃描點數
                this.radius = 100; // BLU半徑 (像素)
                this.centerX = 250;
                this.centerY = 150;
            }

            generateSurfaceData() {
                const data = [];
                for (let i = 0; i < this.points; i++) {
                    const angle = (i / this.points) * 2 * Math.PI;
                    const x = this.centerX + this.radius * Math.cos(angle);
                    const y = this.centerY + this.radius * Math.sin(angle);

                    // 模擬表面高度變化 (-1.00mm 到 +1.00mm 相對於某個基線，這裡假設基線為0)
                    // const baseHeight = 1.25; // 舊的基準
                    // const variation = 0.2 * Math.sin(angle * 3) + 0.12 * Math.cos(angle * 5); // 舊的變化
                    // const noise = (Math.random() - 0.5) * 0.05; // 舊的噪聲
                    // const height = baseHeight + variation + noise; // 舊的高度計算

                    // 新的模擬表面高度，使其在 -1.0 到 +1.0 之間波動
                    const amplitude = 0.8; // 主波動幅度
                    const fine_amplitude = 0.2; // 細微波動幅度
                    const surface_variation = amplitude * Math.sin(angle * 2.5) + fine_amplitude * Math.cos(angle * 5.5);
                    const surface_noise = (Math.random() - 0.5) * 0.1; // 噪聲範圍調整
                    let simulated_raw_height = surface_variation + surface_noise;
                    
                    // 限制在 -1.0 到 1.0 之間
                    simulated_raw_height = Math.max(-1.0, Math.min(1.0, simulated_raw_height));

                    data.push({
                        index: i,
                        angle: angle,
                        x: x,
                        y: y,
                        height: simulated_raw_height, // 使用新的模擬高度
                        targetHeight: parseFloat(document.getElementById('target-height').value), // 目標高度仍從UI讀取
                        compensatedHeight: null,  // 補償後高度，初始為null
                        isCompensated: false      // 是否已經補償
                    });
                }
                return data;
            }
        }

        // 機器人控制器
        class RobotController {
            constructor() {
                this.currentPosition = { x: 0, y: 0, z: 0 };
                this.currentSpeed = 0;
                this.targetSpeed = 0;
                this.maxSpeed = 20; // 應從UI讀取
                this.acceleration = 50; // 應從UI讀取
                // PIDController的構造函數會從UI或initialPidSettings讀取參數
                this.pidController = new PIDController(initialPidSettings.kp, initialPidSettings.ki, initialPidSettings.kd);
            }
 
            updatePIDParameters() { // 此函數現在主要由PIDController內部或UI事件觸發更新其實例參數
                const kp = parseFloat(document.getElementById('kp').value);
                const ki = parseFloat(document.getElementById('ki').value); // UI上的值
                const kd = parseFloat(document.getElementById('kd').value); // UI上的值
                this.pidController.setParameters(kp, ki, kd); // 更新PID實例的參數
            }
 
            calculateSpeed(heightError) {
                if (currentCompensationMode === 'standard') {
                    const standardSpeed = parseFloat(document.getElementById('max-speed').value) || 20;
                    // 標準模式下，不執行PID計算，也不修改this.targetSpeed，直接返回恆定速度
                    return {
                        speed: standardSpeed,
                        pidOutput: 0, // PID未驅動速度
                        error: heightError // 保留原始誤差信息
                    };
                }

                // --- 以下是 PID 和 Hybrid 模式的邏輯 ---
                // 確保PID參數與UI同步
                this.pidController.kp = parseFloat(document.getElementById('kp').value);
                this.pidController.ki = parseFloat(document.getElementById('ki').value);
                this.pidController.kd = parseFloat(document.getElementById('kd').value);
 
                // 使用PID控制器計算速度調整
                const pidResult = this.pidController.update(0, heightError);
 
                // 基礎速度
                const baseSpeed = parseFloat(document.getElementById('max-speed').value);
 
                // 根據高度誤差調整速度 (誤差越大，速度越慢)
                const speedAdjustment = Math.abs(pidResult.output) * ROBOT_SPEED_ADJUSTMENT_FACTOR; // 使用全域可調因子
                this.targetSpeed = Math.max(5, baseSpeed - speedAdjustment); // PID模式下，修改 this.targetSpeed
 
                return {
                    speed: this.targetSpeed,
                    pidOutput: pidResult.output, // PID的原始輸出
                    error: pidResult.error     // 當前誤差
                };
            }
 
            updatePosition(targetPoint, deltaTime) {
                const dx = targetPoint.x - this.currentPosition.x;
                const dy = targetPoint.y - this.currentPosition.y;
                const distance = Math.sqrt(dx * dx + dy * dy);

                if (distance > 1) {
                    const directionX = dx / distance;
                    const directionY = dy / distance;

                    // 速度平滑調整
                    if (currentCompensationMode === 'standard') { // 檢查全域的 currentCompensationMode
                        this.currentSpeed = this.targetSpeed; // 標準模式下，速度直接等於目標速度，無平滑
                    } else {
                        // PID模式使用平滑加速/減速
                        const acceleration = parseFloat(document.getElementById('acceleration').value);
                        if (this.currentSpeed < this.targetSpeed) {
                            this.currentSpeed = Math.min(this.targetSpeed,
                                this.currentSpeed + acceleration * deltaTime);
                        } else {
                            this.currentSpeed = Math.max(this.targetSpeed,
                                this.currentSpeed - acceleration * deltaTime);
                        }
                    }
 
                    const moveDistance = this.currentSpeed * deltaTime;
                    this.currentPosition.x += directionX * moveDistance;
                    this.currentPosition.y += directionY * moveDistance;
                }
            }
        }

        // 塗膠閥控制器
        class DispenseValveController {
            constructor() {
                this.currentFlow = 0;
                this.targetFlow = 0;
                this.stage = 1;
            }

            calculateFlowRate(heightError, speed) {
                // 根據高度誤差選擇塗膠段數
                const absError = Math.abs(heightError);

                if (absError > 0.15) {
                    this.stage = 3; // 高誤差，使用第3段
                    // 讀取百分比，轉換為絕對流量
                    this.targetFlow = (parseFloat(document.getElementById('flow-stage3').value) / 100) * MAX_SCREW_VALVE_FLOW_CM3_S;
                } else if (absError > 0.08) {
                    this.stage = 2; // 中誤差，使用第2段
                    this.targetFlow = (parseFloat(document.getElementById('flow-stage2').value) / 100) * MAX_SCREW_VALVE_FLOW_CM3_S;
                } else {
                    this.stage = 1; // 低誤差，使用第1段
                    this.targetFlow = (parseFloat(document.getElementById('flow-stage1').value) / 100) * MAX_SCREW_VALVE_FLOW_CM3_S;
                }
 
                // 根據速度調整流量
                let speedFactor = speed / parseFloat(document.getElementById('max-speed').value);
                
                // 針對補凹且需要高流量時，調整speedFactor的影響，確保流量能有效提升
                if (heightError > 0.05 && this.stage > 1) { // heightError > 0 表示凹陷，stage > 1 表示選用了較高流量段
                    // 當速度因補凹而降低時，不希望流量也隨之大幅降低
                    // 可以給speedFactor一個下限，或者使用一個不同的調整邏輯
                    const minSpeedFactorForHighFlowCompensation = 0.6; // 示例：確保至少有60%的最大速度下的流量潛力
                    speedFactor = Math.max(speedFactor, minSpeedFactorForHighFlowCompensation);
                }

                this.currentFlow = this.targetFlow * speedFactor;
 
                return {
                    flow: this.currentFlow,
                    stage: this.stage
                };
            }
        }

        // 初始化系統
        const scanner = new BLUSurfaceScanner();
        const robot = new RobotController();
        const valve = new DispenseValveController();

        // 畫布初始化
        const heightCanvas = document.getElementById('height-canvas');
        const heightCtx = heightCanvas.getContext('2d');
        const pidCanvas = document.getElementById('pid-canvas');
        const pidCtx = pidCanvas.getContext('2d');
        const robotCanvas = document.getElementById('robot-canvas');
        const robotCtx = robotCanvas.getContext('2d');
        const profileCanvas = document.getElementById('profile-canvas'); // 新增
        const profileCtx = profileCanvas.getContext('2d'); // 新增
 
        // PID數據記錄
        let pidHistory = [];
        const maxHistoryLength = 200;

        // 工具函數
        function addLog(message, type = 'info') {
            const logPanel = document.getElementById('log-panel');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry';
            logEntry.innerHTML = `
                <span class="log-timestamp">[${timestamp}]</span>
                <span class="log-${type}">${message}</span>
            `;
            logPanel.appendChild(logEntry);
            logPanel.scrollTop = logPanel.scrollHeight;
        }

        function updateStatusDisplay() {
            if (scanData.length > 0 && currentPoint < scanData.length) {
                const point = scanData[currentPoint];
                const heightError = point.targetHeight - point.height;

                document.getElementById('current-position').textContent =
                    `(${point.x.toFixed(1)}, ${point.y.toFixed(1)})`;
                document.getElementById('robot-speed').textContent =
                    `${robot.currentSpeed.toFixed(1)} mm/s`;
                document.getElementById('flow-rate').textContent =
                    `${valve.currentFlow.toFixed(2)} cm³/s`;
                document.getElementById('height-error').textContent =
                    `${heightError.toFixed(3)} mm`;

                const progress = (currentPoint / scanData.length) * 100;
                document.getElementById('progress-percent').textContent = `${progress.toFixed(1)}%`;
                document.getElementById('progress-fill').style.width = `${progress}%`;
            }
        }

        // 繪圖函數
        function drawHeightMap() {
            heightCtx.clearRect(0, 0, heightCanvas.width, heightCanvas.height);

            if (scanData.length === 0) return;

            // 繪製BLU輪廓
            heightCtx.strokeStyle = '#2c3e50';
            heightCtx.lineWidth = 2;
            heightCtx.beginPath();
            heightCtx.arc(scanner.centerX, scanner.centerY, scanner.radius, 0, 2 * Math.PI);
            heightCtx.stroke();

            // 繪製高度數據點
            scanData.forEach((point, index) => {
                // 新的正規化：將 -1.0mm 映射到 0，+1.0mm 映射到 1
                const normalizedHeight = (point.height - (-1.0)) / (1.0 - (-1.0)); // (point.height + 1.0) / 2.0
                const hue = (1 - normalizedHeight) * 240; // 藍色 (normalizedHeight=0, height=-1.0) 到 紅色 (normalizedHeight=1, height=1.0)
                heightCtx.fillStyle = `hsl(${hue}, 70%, 50%)`;
 
                heightCtx.beginPath();
                heightCtx.arc(point.x, point.y, 3, 0, 2 * Math.PI);
                heightCtx.fill();

                // 標示當前點
                if (index === currentPoint && isRunning) {
                    heightCtx.strokeStyle = '#fff';
                    heightCtx.lineWidth = 3;
                    heightCtx.beginPath();
                    heightCtx.arc(point.x, point.y, 6, 0, 2 * Math.PI);
                    heightCtx.stroke();
                }
            });

            // 繪製圖例
            const legendY = 20;
            const legendXStart = 20;
            const legendBarWidth = 15;
            const numLegendSteps = 10;
            for (let i = 0; i <= numLegendSteps; i++) {
                // 使用與數據點相同的正規化和色相計算
                const normalizedLegendHeight = i / numLegendSteps; // 從 0 到 1
                const hue = (1 - normalizedLegendHeight) * 240; // 從藍色到紅色
                heightCtx.fillStyle = `hsl(${hue}, 70%, 50%)`;
                heightCtx.fillRect(legendXStart + i * legendBarWidth, legendY, legendBarWidth, 12);
            }
 
            heightCtx.fillStyle = '#2c3e50';
            heightCtx.font = '12px Arial';
            heightCtx.textAlign = 'left';
            heightCtx.fillText('-1.0mm', legendXStart, legendY + 25);
            heightCtx.textAlign = 'right';
            heightCtx.fillText('+1.0mm', legendXStart + (numLegendSteps + 1) * legendBarWidth, legendY + 25);
            heightCtx.textAlign = 'left'; // 重置
        }
 
        function drawPIDResponse() {
            pidCtx.clearRect(0, 0, pidCanvas.width, pidCanvas.height);

            if (pidHistory.length < 2) return;

            // 繪製網格
            pidCtx.strokeStyle = '#ecf0f1';
            pidCtx.lineWidth = 1;
            for (let i = 0; i <= 10; i++) {
                const y = (i / 10) * pidCanvas.height;
                pidCtx.beginPath();
                pidCtx.moveTo(0, y);
                pidCtx.lineTo(pidCanvas.width, y);
                pidCtx.stroke();
            }

            // 繪製PID輸出曲線
            pidCtx.strokeStyle = '#3498db';
            pidCtx.lineWidth = 2;
            pidCtx.beginPath();

            pidHistory.forEach((data, index) => {
                const x = (index / maxHistoryLength) * pidCanvas.width;
                const y = pidCanvas.height / 2 - data.output * 20; // 縮放因子

                if (index === 0) {
                    pidCtx.moveTo(x, y);
                } else {
                    pidCtx.lineTo(x, y);
                }
            });
            pidCtx.stroke();

            // 繪製誤差曲線
            pidCtx.strokeStyle = '#e74c3c';
            pidCtx.lineWidth = 2;
            pidCtx.beginPath();

            pidHistory.forEach((data, index) => {
                const x = (index / maxHistoryLength) * pidCanvas.width;
                const y = pidCanvas.height / 2 - data.error * 100; // 縮放因子

                if (index === 0) {
                    pidCtx.moveTo(x, y);
                } else {
                    pidCtx.lineTo(x, y);
                }
            });
            pidCtx.stroke();

            // 繪製中心線
            pidCtx.strokeStyle = '#95a5a6';
            pidCtx.lineWidth = 1;
            pidCtx.setLineDash([5, 5]);
            pidCtx.beginPath();
            pidCtx.moveTo(0, pidCanvas.height / 2);
            pidCtx.lineTo(pidCanvas.width, pidCanvas.height / 2);
            pidCtx.stroke();
            pidCtx.setLineDash([]);

            // 圖例
            pidCtx.fillStyle = '#2c3e50';
            pidCtx.font = '12px Arial';
            pidCtx.fillText('PID輸出', 10, 20);
            pidCtx.fillStyle = '#3498db';
            pidCtx.fillRect(70, 12, 15, 3);
            pidCtx.fillStyle = '#2c3e50';
            pidCtx.fillText('誤差', 100, 20);
            pidCtx.fillStyle = '#e74c3c';
            pidCtx.fillRect(130, 12, 15, 3);
        }

        // 獲取高度對應的顏色
        function getHeightColor(height, alpha = 1) {
            const normalizedHeight = (height - 0.8) / 0.2; // 正規化到0-1 (0.8mm-1.0mm)
            const hue = (1 - normalizedHeight) * 240; // 藍色到紅色 (240度到0度)
            return `hsla(${hue}, 70%, 50%, ${alpha})`;
        }

        // 獲取補償後的顏色（根據膠高公差設定顏色）
        function getCompensatedColor(originalHeight, compensatedHeight, alpha = 1) {
            if (compensatedHeight === null) {
                // 未補償時使用原始高度顏色
                return getHeightColor(originalHeight, alpha);
            } else {
                // 補償後根據膠高公差設定顏色
                return getGlueHeightColor(compensatedHeight, alpha);
            }
        }

        // 根據膠高公差設定顏色
        function getGlueHeightColor(height, alpha = 1) {
            const targetHeight = parseFloat(document.getElementById('target-height').value) || 1.2;
            const tolerance = parseFloat(document.getElementById('tolerance').value) || 0.2; // 從UI獲取公差設定
            const warningZone = tolerance * 0.25; // 公差邊緣警告區域為公差的25%

            const deviation = Math.abs(height - targetHeight);

            if (deviation <= tolerance - warningZone) {
                // 標準公差內：綠色
                return `hsla(120, 70%, 50%, ${alpha})`;
            } else if (deviation <= tolerance) {
                // 公差允收邊緣：黃綠色
                return `hsla(90, 70%, 50%, ${alpha})`;
            } else if (deviation <= tolerance + warningZone) {
                // 公差外接近公差：黃色
                return `hsla(60, 70%, 50%, ${alpha})`;
            } else {
                // 公差外：紅色
                return `hsla(0, 70%, 50%, ${alpha})`;
            }
        }

        function drawRobotPath() {
            robotCtx.clearRect(0, 0, robotCanvas.width, robotCanvas.height);

            if (scanData.length === 0) return;

            // 繪製BLU輪廓
            robotCtx.strokeStyle = '#2c3e50';
            robotCtx.lineWidth = 2;
            robotCtx.beginPath();
            robotCtx.arc(scanner.centerX, scanner.centerY, scanner.radius, 0, 2 * Math.PI);
            robotCtx.stroke();

            // 繪製已完成的路徑（顯示補償後的顏色）
            if (currentPoint > 0) {
                robotCtx.lineWidth = 4;

                for (let i = 0; i < currentPoint - 1; i++) {
                    const point1 = scanData[i];
                    const point2 = scanData[i + 1];

                    // 使用補償後的高度來決定顏色
                    const compensatedColor = getCompensatedColor(point1.height, point1.compensatedHeight);
                    robotCtx.strokeStyle = compensatedColor;

                    robotCtx.beginPath();
                    robotCtx.moveTo(point1.x, point1.y);
                    robotCtx.lineTo(point2.x, point2.y);
                    robotCtx.stroke();
                }
            }

            // 繪製未完成的路徑（半透明高度顏色）
            if (currentPoint < scanData.length - 1) {
                robotCtx.lineWidth = 2;
                robotCtx.setLineDash([3, 3]);

                for (let i = currentPoint; i < scanData.length - 1; i++) {
                    const point1 = scanData[i];
                    const point2 = scanData[i + 1];

                    // 使用半透明的高度顏色
                    const heightColor = getHeightColor(point1.height, 0.4);
                    robotCtx.strokeStyle = heightColor;

                    robotCtx.beginPath();
                    robotCtx.moveTo(point1.x, point1.y);
                    robotCtx.lineTo(point2.x, point2.y);
                    robotCtx.stroke();
                }
                robotCtx.setLineDash([]);
            }

            // 繪製高度數據點（補償前後對比）
            scanData.forEach((point, index) => {
                // 已完成的點顯示補償後顏色，未完成的點顯示原始顏色
                const color = index < currentPoint ?
                    getCompensatedColor(point.height, point.compensatedHeight) :
                    getHeightColor(point.height, 0.6);

                robotCtx.fillStyle = color;

                // 已完成的點顯示較大
                const radius = index < currentPoint ? 4 : 2;
                const alpha = index < currentPoint ? 1.0 : 0.6;

                robotCtx.globalAlpha = alpha;
                robotCtx.beginPath();
                robotCtx.arc(point.x, point.y, radius, 0, 2 * Math.PI);
                robotCtx.fill();

                // 為已補償的點添加邊框指示
                if (index < currentPoint && point.isCompensated) {
                    robotCtx.strokeStyle = '#fff';
                    robotCtx.lineWidth = 1;
                    robotCtx.beginPath();
                    robotCtx.arc(point.x, point.y, radius + 1, 0, 2 * Math.PI);
                    robotCtx.stroke();
                }

                robotCtx.globalAlpha = 1.0;
            });

            // 繪製機器人當前位置（根據補償狀態顯示顏色）
            if (isRunning && currentPoint < scanData.length) {
                const currentPointData = scanData[currentPoint];

                // 如果當前點已經補償，顯示補償後顏色，否則顯示原始顏色
                const currentColor = currentPointData.isCompensated ?
                    getCompensatedColor(currentPointData.height, currentPointData.compensatedHeight) :
                    getHeightColor(currentPointData.height);

                // 繪製機器人主體
                robotCtx.fillStyle = currentColor;
                robotCtx.beginPath();
                robotCtx.arc(robot.currentPosition.x, robot.currentPosition.y, 8, 0, 2 * Math.PI);
                robotCtx.fill();

                // 繪製白色邊框以突出顯示
                robotCtx.strokeStyle = '#fff';
                robotCtx.lineWidth = 2;
                robotCtx.beginPath();
                robotCtx.arc(robot.currentPosition.x, robot.currentPosition.y, 8, 0, 2 * Math.PI);
                robotCtx.stroke();

                // 繪製方向指示（使用相同的補償顏色）
                if (currentPoint < scanData.length - 1) {
                    const nextPoint = scanData[currentPoint + 1];
                    const dx = nextPoint.x - robot.currentPosition.x;
                    const dy = nextPoint.y - robot.currentPosition.y;
                    const angle = Math.atan2(dy, dx);

                    robotCtx.strokeStyle = currentColor;
                    robotCtx.lineWidth = 3;
                    robotCtx.beginPath();
                    robotCtx.moveTo(robot.currentPosition.x, robot.currentPosition.y);
                    robotCtx.lineTo(
                        robot.currentPosition.x + Math.cos(angle) * 15,
                        robot.currentPosition.y + Math.sin(angle) * 15
                    );
                    robotCtx.stroke();

                    // 繪製箭頭
                    const arrowLength = 5;
                    const arrowAngle = Math.PI / 6;
                    const endX = robot.currentPosition.x + Math.cos(angle) * 15;
                    const endY = robot.currentPosition.y + Math.sin(angle) * 15;

                    robotCtx.beginPath();
                    robotCtx.moveTo(endX, endY);
                    robotCtx.lineTo(
                        endX - arrowLength * Math.cos(angle - arrowAngle),
                        endY - arrowLength * Math.sin(angle - arrowAngle)
                    );
                    robotCtx.moveTo(endX, endY);
                    robotCtx.lineTo(
                        endX - arrowLength * Math.cos(angle + arrowAngle),
                        endY - arrowLength * Math.sin(angle + arrowAngle)
                    );
                    robotCtx.stroke();
                }
            }

            // 繪製塗膠流量指示
            if (isRunning && currentPoint < scanData.length) {
                const point = scanData[currentPoint];
                const flowStage = valve.stage;
                const colors = ['#3498db', '#f39c12', '#e74c3c'];

                robotCtx.fillStyle = colors[flowStage - 1];
                robotCtx.font = '12px Arial';
                robotCtx.fillText(`段${flowStage}`, point.x + 10, point.y - 10);
            }

            // 繪製膠高公差顏色圖例 (移到左上角避免擋到圓圈)
            const legendX = 20;
            const legendY = 20;
            const legendWidth = 80; // 縮小一半寬度
            const legendHeight = 12;

            // 繪製圖例背景
            robotCtx.fillStyle = 'rgba(255, 255, 255, 0.95)';
            robotCtx.fillRect(legendX - 10, legendY - 5, legendWidth + 40, legendHeight + 80);
            robotCtx.strokeStyle = '#2c3e50';
            robotCtx.lineWidth = 1;
            robotCtx.strokeRect(legendX - 10, legendY - 5, legendWidth + 40, legendHeight + 80);

            // 繪製標題
            robotCtx.fillStyle = '#2c3e50';
            robotCtx.font = '11px Arial';
            robotCtx.fillText('塗膠高度品質', legendX, legendY - 8);

            // 繪製顏色示例
            const colorSamples = [
                { color: 'hsla(120, 70%, 50%, 1)', label: '標準公差內', y: legendY + 5 },
                { color: 'hsla(90, 70%, 50%, 1)', label: '公差邊緣', y: legendY + 20 },
                { color: 'hsla(60, 70%, 50%, 1)', label: '接近公差外', y: legendY + 35 },
                { color: 'hsla(0, 70%, 50%, 1)', label: '公差外', y: legendY + 50 }
            ];

            colorSamples.forEach(sample => {
                // 繪製顏色方塊
                robotCtx.fillStyle = sample.color;
                robotCtx.fillRect(legendX, sample.y, 12, 10);

                // 繪製邊框
                robotCtx.strokeStyle = '#2c3e50';
                robotCtx.lineWidth = 1;
                robotCtx.strokeRect(legendX, sample.y, 12, 10);

                // 繪製標籤
                robotCtx.fillStyle = '#2c3e50';
                robotCtx.font = '9px Arial';
                robotCtx.fillText(sample.label, legendX + 18, sample.y + 8);
            });

            // 繪製公差說明
            robotCtx.font = '8px Arial';
            robotCtx.fillText('目標: 1.2mm ±0.1mm', legendX, legendY + 68);
        }

        // 新增：繪製塗膠剖面趨勢圖函數 (框架)
        function drawProfileChart() {
            profileCtx.clearRect(0, 0, profileCanvas.width, profileCanvas.height);
            if (scanData.length === 0) {
                profileCtx.fillStyle = '#7f8c8d';
                profileCtx.textAlign = 'center';
                profileCtx.font = '16px Arial';
                profileCtx.fillText('請先掃描以生成剖面數據', profileCanvas.width / 2, profileCanvas.height / 2);
                return;
            }

            const padding = 40; // 增加padding以便容納軸標籤
            const chartWidth = profileCanvas.width - 2 * padding;
            const chartHeight = profileCanvas.height - 2 * padding;

            const targetHeightControlVal = parseFloat(document.getElementById('target-height').value); // 實際控制用的目標高度
            const toleranceVal = parseFloat(document.getElementById('tolerance').value);
            const chartBaselineY = 0.0; // 圖表上目標高度的顯示基準線

            // 計算Y軸範圍
            let allHeights = scanData.map(p => p.height); // 包含原始表面高度 (-1.0 to 1.0)
            if (isRunning || currentPoint > 0 || scanData.some(p => p.isCompensated)) {
                 scanData.forEach(p => {
                    if (p.compensatedHeight !== null) allHeights.push(p.compensatedHeight); // 包含補償後高度
                });
            }
            // 包含控制目標的公差範圍，以及圖表Y軸的強制最小值
            allHeights.push(targetHeightControlVal - toleranceVal, targetHeightControlVal + toleranceVal, targetHeightControlVal);
            
            let minVal = -0.10; // Y軸最小值固定為 -0.10mm
            let maxVal = Math.max(...allHeights.filter(h => !isNaN(h))); // 取數據中的最大值
            maxVal = Math.max(maxVal, targetHeightControlVal + toleranceVal, chartBaselineY + 0.1); // 確保最大值至少能包含公差上限和基準線上一點
            minVal = Math.min(minVal, ...allHeights.filter(h => !isNaN(h)), targetHeightControlVal - toleranceVal, chartBaselineY -0.1); // 確保最小值能包含公差下限和基準線下一點

            // 如果計算出的maxVal小於-0.1，則調整maxVal以保證有合理的顯示範圍
            if (maxVal <= minVal) {
                maxVal = minVal + 1.0; // 至少1mm的顯示範圍
            }

            const yRange = maxVal - minVal;
 
            if (yRange <= 0 || isNaN(yRange)) { // 避免除以零或無效範圍 (yRange應大於0)
                profileCtx.fillStyle = '#7f8c8d';
                profileCtx.textAlign = 'center';
                profileCtx.font = '16px Arial';
                profileCtx.fillText('數據範圍無效或無變化', profileCanvas.width / 2, profileCanvas.height / 2);
                return;
            }

            const scaleY = chartHeight / yRange;
            const scaleX = chartWidth / (scanData.length > 1 ? scanData.length - 1 : 1); // 避免scanData只有一個點時除以0

            function getY(value) {
                return padding + chartHeight - (value - minVal) * scaleY;
            }

            function getX(index) {
                return padding + index * scaleX;
            }

            // 繪製網格線和Y軸標籤
            profileCtx.strokeStyle = '#e0e0e0';
            profileCtx.lineWidth = 0.5;
            profileCtx.font = '10px Arial';
            profileCtx.fillStyle = '#555';
            profileCtx.textAlign = 'right';

            const numYGridLines = 5;
            for (let i = 0; i <= numYGridLines; i++) {
                const val = minVal + (yRange / numYGridLines) * i;
                const yPos = getY(val);
                profileCtx.beginPath();
                profileCtx.moveTo(padding, yPos);
                profileCtx.lineTo(padding + chartWidth, yPos);
                profileCtx.stroke();
                profileCtx.fillText(val.toFixed(2), padding - 8, yPos + 4);
            }
            
            // 繪製X軸標籤 (簡化，只標示起點和終點)
            profileCtx.textAlign = 'center';
            profileCtx.fillText('0', padding, padding + chartHeight + 15);
            if (scanData.length > 1) {
                profileCtx.fillText((scanData.length -1).toString(), padding + chartWidth, padding + chartHeight + 15);
            }
            profileCtx.fillText('掃描點索引', profileCanvas.width / 2, padding + chartHeight + 28);


            // 繪製線條函數
            function drawLine(dataPoints, color, lineWidth = 2, dashed = false, connectNaN = false) {
                profileCtx.strokeStyle = color;
                profileCtx.lineWidth = lineWidth;
                if (dashed) {
                    profileCtx.setLineDash([5, 3]);
                }
                
                let firstPoint = true;
                profileCtx.beginPath();

                dataPoints.forEach((value, index) => {
                    if (isNaN(value) && !connectNaN) {
                        if (!firstPoint) profileCtx.stroke(); // 結束上一段
                        firstPoint = true; // 下一個有效點將是新線段的開始
                        return;
                    }
                    // 如果 connectNaN 為 true，或者值有效，則繼續繪製
                    const x = getX(index);
                    const y = getY(isNaN(value) ? (minVal + maxVal)/2 : value); // 如果是NaN且要連接，則畫在中間 (可調整)

                    if (firstPoint || (isNaN(value) && connectNaN && index > 0 && isNaN(dataPoints[index-1])) ) { // 開始新線段或處理連續NaN
                        profileCtx.moveTo(x,y);
                        firstPoint = false;
                    } else {
                         profileCtx.lineTo(x, y);
                    }
                });
                if(!firstPoint) profileCtx.stroke(); // 確保最後一段被繪製

                if (dashed) {
                    profileCtx.setLineDash([]);
                }
            }

            // 1. 目標高度基準線 (繪製在 Y=0.00mm 位置)
            const targetBaselineData = scanData.map(() => chartBaselineY);
            drawLine(targetBaselineData, 'green', 1.5);
 
            // 2. 實際控制目標的公差線 (相對於 targetHeightControlVal)
            const upperToleranceData = scanData.map(() => targetHeightControlVal + toleranceVal);
            const lowerToleranceData = scanData.map(() => targetHeightControlVal - toleranceVal);
            drawLine(upperToleranceData, 'rgba(255, 165, 0, 0.7)', 1, true); // Orange, semi-transparent
            drawLine(lowerToleranceData, 'rgba(255, 165, 0, 0.7)', 1, true); // Orange, semi-transparent
 
            // 3. 原始表面高度線 (數據範圍 -1.0 to 1.0)
            const originalSurfaceData = scanData.map(p => p.height);
            drawLine(originalSurfaceData, 'blue', 1.5);

            // 4. 補償後高度線 (只畫已補償的點，斷開NaN)
            const compensatedData = scanData.map(p => (p.compensatedHeight !== null && p.isCompensated) ? p.compensatedHeight : NaN);
            drawLine(compensatedData, 'red', 2, false, false);


            // 繪製圖例
            const legendYStart = padding / 3;
            const legendXStart = padding;
            const legendItemHeight = 15;
            const legendBoxSize = 10;
            profileCtx.font = '10px Arial';
            profileCtx.textAlign = 'left';

            function drawLegendItem(text, color, x, y) {
                profileCtx.fillStyle = color;
                profileCtx.fillRect(x, y - legendBoxSize / 1.5, legendBoxSize, legendBoxSize);
                profileCtx.fillStyle = '#333';
                profileCtx.fillText(text, x + legendBoxSize + 5, y);
            }
 
            drawLegendItem('目標基準 (0.0mm)', 'green', legendXStart, legendYStart);
            drawLegendItem('控制公差', 'rgba(255, 165, 0, 0.7)', legendXStart + 120, legendYStart);
            drawLegendItem('原始表面', 'blue', legendXStart + 220, legendYStart);
            drawLegendItem('補償後高度', 'red', legendXStart + 320, legendYStart);

            // 繪製滑鼠懸停tooltip
            if (showTooltip && tooltipData) {
                const tooltipX = mouseX + 10;
                const tooltipY = mouseY - 10;

                // 準備tooltip文字
                let tooltipText = `點位 ${tooltipData.pointIndex}`;
                let heightText = '';
                let statusColor = '#333';

                if (tooltipData.compensatedHeight !== null) {
                    heightText = `高度: ${tooltipData.compensatedHeight.toFixed(3)}mm`;
                    statusColor = tooltipData.withinTolerance ? '#28a745' : '#dc3545';
                } else {
                    heightText = `原始高度: ${tooltipData.originalHeight.toFixed(3)}mm`;
                    statusColor = '#6c757d';
                }

                // 計算tooltip背景尺寸
                profileCtx.font = '12px Arial';
                const textMetrics1 = profileCtx.measureText(tooltipText);
                const textMetrics2 = profileCtx.measureText(heightText);
                const tooltipWidth = Math.max(textMetrics1.width, textMetrics2.width) + 16;
                const tooltipHeight = 40;

                // 調整tooltip位置避免超出畫布
                let finalTooltipX = tooltipX;
                let finalTooltipY = tooltipY;

                if (tooltipX + tooltipWidth > profileCanvas.width) {
                    finalTooltipX = mouseX - tooltipWidth - 10;
                }
                if (tooltipY - tooltipHeight < 0) {
                    finalTooltipY = mouseY + 20;
                }

                // 繪製tooltip背景
                profileCtx.fillStyle = 'rgba(0, 0, 0, 0.8)';
                profileCtx.fillRect(finalTooltipX, finalTooltipY - tooltipHeight, tooltipWidth, tooltipHeight);

                // 繪製tooltip邊框
                profileCtx.strokeStyle = '#fff';
                profileCtx.lineWidth = 1;
                profileCtx.strokeRect(finalTooltipX, finalTooltipY - tooltipHeight, tooltipWidth, tooltipHeight);

                // 繪製tooltip文字
                profileCtx.fillStyle = '#fff';
                profileCtx.font = '11px Arial';
                profileCtx.textAlign = 'left';
                profileCtx.fillText(tooltipText, finalTooltipX + 8, finalTooltipY - 22);

                profileCtx.fillStyle = statusColor;
                profileCtx.font = 'bold 11px Arial';
                profileCtx.fillText(heightText, finalTooltipX + 8, finalTooltipY - 8);
            }
        }
 
        // 主要控制函數
        function startScanning() {
            if (isScanning) return;

            isScanning = true;
            document.getElementById('scan-btn').disabled = true;
            addLog('開始掃描BLU表面高度...', 'info');

            // 模擬掃描過程
            let scanProgress = 0;
            const scanInterval = setInterval(() => {
                scanProgress += 10;
                addLog(`掃描進度: ${scanProgress}%`, 'info');

                if (scanProgress >= 100) {
                    clearInterval(scanInterval);
                    scanData = scanner.generateSurfaceData();
                    isScanning = false;

                    addLog(`掃描完成！檢測到 ${scanData.length} 個測量點`, 'success');
                    addLog('分析表面高度變化...', 'info');

                    // 分析掃描結果
                    const heights = scanData.map(p => p.height);
                    const minHeight = Math.min(...heights);
                    const maxHeight = Math.max(...heights);
                    const avgHeight = heights.reduce((a, b) => a + b) / heights.length;

                    addLog(`高度範圍: ${minHeight.toFixed(3)}mm - ${maxHeight.toFixed(3)}mm`, 'info');
                    addLog(`平均高度: ${avgHeight.toFixed(3)}mm`, 'info');

                    document.getElementById('scan-btn').disabled = false;
                    document.getElementById('start-btn').disabled = false;

                    drawHeightMap();
                    updateHeightDataTable(); // 更新數據表格
                    drawProfileChart(); // 新增：掃描完成後也繪製剖面圖
                }
            }, 200);
        }
 
        // 移除優化參數功能
        function removedOptimizeParameters() {
            if (scanData.length === 0) {
                addLog('請先進行表面掃描', 'warning');
                return;
            }

            // 啟用智能優化模式
            isOptimizationEnabled = true;

            addLog('� 啟用智能優化控制模式', 'success');
            addLog('📊 系統將根據每個點的高度差動態調整:', 'info');
            addLog('   • 機器人移動速度 (5-30 mm/s)', 'info');
            addLog('   • 塗膠流量控制 (0.2-1.0 cm³/s)', 'info');
            addLog('   • 螺桿閥開度 (20%-100%)', 'info');
            addLog('   • PID補償強度 (50%-95%)', 'info');

            // 分析表面數據
            const heights = scanData.map(point => point.height);
            const minHeight = Math.min(...heights);
            const maxHeight = Math.max(...heights);
            const avgHeight = heights.reduce((a, b) => a + b) / heights.length;
            const targetHeight = parseFloat(document.getElementById('target-height').value) || 1.2;

            addLog(`� 表面分析完成:`, 'info');
            addLog(`   高度範圍: ${minHeight.toFixed(3)}mm - ${maxHeight.toFixed(3)}mm`, 'info');
            addLog(`   平均高度: ${avgHeight.toFixed(3)}mm`, 'info');
            addLog(`   目標高度: ${targetHeight.toFixed(3)}mm`, 'info');

            // 為每個點預計算優化參數
            scanData.forEach((point, index) => {
                const heightError = targetHeight - point.height;
                const absError = Math.abs(heightError);

                // 計算機器人速度 (高度差越大，速度越慢)
                let robotSpeed;
                if (absError > 0.15) {
                    robotSpeed = 8;  // 慢速，精細控制
                } else if (absError > 0.08) {
                    robotSpeed = 15; // 中速
                } else {
                    robotSpeed = 25; // 快速
                }

                // 計算塗膠流量 (根據需要補償的高度)
                let flowRate;
                if (heightError > 0.1) {
                    flowRate = 0.9;  // 需要大量補償
                } else if (heightError > 0.05) {
                    flowRate = 0.7;  // 中等補償
                } else if (heightError > -0.05) {
                    flowRate = 0.5;  // 標準流量
                } else {
                    flowRate = 0.3;  // 減少流量
                }

                // 計算螺桿閥開度 (現在直接使用百分比作為參考，或基於計算出的絕對flowRate反推一個等效開度百分比)
                // 如果 optimizeParameters 旨在直接輸出一個“閥門開度百分比”，那麼這裡的 flowRate 計算可能更多是爲了物理模擬
                // 假設 optimizeParameters 中的 valveOpening 就是期望的閥門開度百分比
                const valveOpening = Math.min(100, Math.max(20, flowRate / MAX_SCREW_VALVE_FLOW_CM3_S * 100 * 1.2)); // 根據計算出的絕對流量反推並稍作放大估算開度

                // 計算補償強度 (高度差越大，補償越強)
                const compensationStrength = Math.min(95, 50 + absError * 300);

                // 存儲優化參數
                point.optimizedParams = {
                    robotSpeed: robotSpeed,
                    flowRate: flowRate,
                    valveOpening: valveOpening,
                    compensationStrength: compensationStrength,
                    heightError: heightError
                };
            });

            addLog(`✅ 智能優化參數計算完成!`, 'success');
            addLog(`🎯 現在開始塗膠將使用動態優化控制`, 'success');

            // 顯示優化統計
            const avgSpeed = scanData.reduce((sum, p) => sum + p.optimizedParams.robotSpeed, 0) / scanData.length;
            const avgFlow = scanData.reduce((sum, p) => sum + p.optimizedParams.flowRate, 0) / scanData.length;
            const avgValve = scanData.reduce((sum, p) => sum + p.optimizedParams.valveOpening, 0) / scanData.length;

            addLog(`📊 優化參數統計:`, 'info');
            addLog(`   平均機器人速度: ${avgSpeed.toFixed(1)} mm/s`, 'info');
            addLog(`   平均塗膠流量: ${avgFlow.toFixed(2)} cm³/s`, 'info');
            addLog(`   平均螺桿閥開度: ${avgValve.toFixed(1)}%`, 'info');
        }

        function startDispensing() {
            if (isRunning || scanData.length === 0) return;

            isRunning = true;
            isPaused = false;
            currentPoint = 0;
            startTime = Date.now();

            // 重置控制器
            robot.pidController.reset();
            robot.currentPosition = { x: scanData[0].x, y: scanData[0].y, z: 0 };
            pidHistory = [];

            document.getElementById('start-btn').disabled = true;
            document.getElementById('pause-btn').disabled = false;
            document.getElementById('stop-btn').disabled = false;

            addLog('開始邊緣塗膠作業...', 'success');
            addLog('PID控制器已初始化', 'info');

            animationLoop();
        }

        function pauseDispensing() {
            if (!isRunning) return;

            isPaused = !isPaused;
            const pauseBtn = document.getElementById('pause-btn');

            if (isPaused) {
                pauseBtn.textContent = '▶️ 繼續';
                addLog('作業已暫停', 'warning');
            } else {
                pauseBtn.textContent = '⏸️ 暫停';
                addLog('作業已恢復', 'info');
                animationLoop();
            }
        }

        function stopDispensing() {
            if (!isRunning) return;

            isRunning = false;
            isPaused = false;

            if (animationId) {
                cancelAnimationFrame(animationId);
                animationId = null;
            }

            document.getElementById('start-btn').disabled = false;
            document.getElementById('pause-btn').disabled = true;
            document.getElementById('stop-btn').disabled = true;
            document.getElementById('pause-btn').textContent = '⏸️ 暫停';

            // 移除優化統計顯示

            addLog('作業已停止', 'warning');

            // 重置狀態顯示
            document.getElementById('current-position').textContent = '待機中';
            document.getElementById('robot-speed').textContent = '0 mm/s';
            document.getElementById('flow-rate').textContent = '0 cm³/s';
            document.getElementById('height-error').textContent = '0 mm';
            document.getElementById('pid-output').textContent = '0';
        }

        // 顯示優化控制結果統計
        function showOptimizationResults() {
            const completedPoints = scanData.slice(0, currentPoint).filter(p => p.actualControlParams);

            if (completedPoints.length === 0) return;

            addLog('📊 智能優化控制統計結果:', 'success');

            // 計算平均值
            const avgSpeed = completedPoints.reduce((sum, p) => sum + p.actualControlParams.robotSpeed, 0) / completedPoints.length;
            const avgFlow = completedPoints.reduce((sum, p) => sum + p.actualControlParams.flowRate, 0) / completedPoints.length;
            const avgValve = completedPoints.reduce((sum, p) => sum + p.actualControlParams.valveOpening, 0) / completedPoints.length;

            // 計算品質統計
            const qualityStats = calculateQualityStats(completedPoints);

            addLog(`   處理點數: ${completedPoints.length}`, 'info');
            addLog(`   平均機器人速度: ${avgSpeed.toFixed(1)} mm/s`, 'info');
            addLog(`   平均塗膠流量: ${avgFlow.toFixed(2)} cm³/s`, 'info');
            addLog(`   平均螺桿閥開度: ${avgValve.toFixed(1)}%`, 'info');
            addLog(`   🎯 補償達標率: ${qualityStats.qualityRate.toFixed(1)}% (${qualityStats.withinTolerance}/${qualityStats.totalPoints}點)`, qualityStats.qualityRate >= 80 ? 'success' : 'warning');
            addLog(`   📊 平均誤差: ${qualityStats.avgError.toFixed(3)}mm，最大誤差: ${qualityStats.maxError.toFixed(3)}mm`, 'info');

            // 顯示速度分佈統計
            const speedStats = {
                slow: completedPoints.filter(p => p.actualControlParams.robotSpeed <= 10).length,
                medium: completedPoints.filter(p => p.actualControlParams.robotSpeed > 10 && p.actualControlParams.robotSpeed <= 20).length,
                fast: completedPoints.filter(p => p.actualControlParams.robotSpeed > 20).length
            };

            addLog(`   速度分佈: 慢速${speedStats.slow}點, 中速${speedStats.medium}點, 快速${speedStats.fast}點`, 'info');
        }

        // 計算品質統計
        function calculateQualityStats(points) {
            const targetHeight = parseFloat(document.getElementById('target-height').value) || 1.2;
            const tolerance = parseFloat(document.getElementById('tolerance').value) || 0.2; // 從UI獲取公差設定

            let withinTolerance = 0;
            let totalCompensatedPoints = 0;
            let avgError = 0;
            let maxError = 0;

            points.forEach(point => {
                if (point.compensatedHeight) {
                    totalCompensatedPoints++;
                    const deviation = Math.abs(point.compensatedHeight - targetHeight);
                    avgError += deviation;
                    maxError = Math.max(maxError, deviation);

                    if (deviation <= tolerance) {
                        withinTolerance++;
                    }
                }
            });

            avgError = totalCompensatedPoints > 0 ? avgError / totalCompensatedPoints : 0;

            return {
                qualityRate: totalCompensatedPoints > 0 ? (withinTolerance / totalCompensatedPoints) * 100 : 0,
                withinTolerance: withinTolerance,
                totalPoints: totalCompensatedPoints,
                avgError: avgError,
                maxError: maxError
            };
        }

        function animationLoop() {
            if (!isRunning || isPaused) return;

            const currentTime = Date.now();
            const deltaTime = startTime ? (currentTime - startTime) / 1000 : 0;
            startTime = currentTime;

            if (currentPoint < scanData.length) {
                const targetPoint = scanData[currentPoint];
                const heightError = targetPoint.targetHeight - targetPoint.height;

                // 計算機器人速度 (使用PID控制)
                const speedResult = robot.calculateSpeed(heightError);

                // 計算塗膠流量
                const flowResult = valve.calculateFlowRate(heightError, speedResult.speed);

                // 更新機器人位置
                robot.updatePosition(targetPoint, deltaTime);

                // 記錄PID數據
                pidHistory.push({
                    output: speedResult.pidOutput,
                    error: speedResult.error,
                    timestamp: currentTime
                });

                if (pidHistory.length > maxHistoryLength) {
                    pidHistory.shift();
                }

                // 更新狀態顯示
                document.getElementById('pid-output').textContent = speedResult.pidOutput.toFixed(3);
                updateStatusDisplay();

                // 檢查是否到達目標點
                const distance = Math.sqrt(
                    Math.pow(robot.currentPosition.x - targetPoint.x, 2) +
                    Math.pow(robot.currentPosition.y - targetPoint.y, 2)
                );

                if (distance < 2) {
                    // 計算補償後的高度（塗膠後高度範圍1.0~1.5mm）
                    const targetHeight = parseFloat(document.getElementById('target-height').value) || 1.2;
                    const errorForCompensation = targetHeight - targetPoint.height; // 原始誤差
                    const pidHeightContribution = -speedResult.pidOutput * PID_OUTPUT_TO_HEIGHT_FACTOR; // 使用全域可調因子，並反轉符號
 
                    let actualRobotSpeed = speedResult.speed; // 預設為PID計算的速度 (受max-speed影響)
                    let actualFlowRate = flowResult.flow;     // 預設為calculateFlowRate計算的流量 (受段位和速度影響)
                    let actualCompensationFactor = 0.8;       // 高度補償時的基礎補償因子
                    let actualValveOpening = valve.stage * 30 + 10; // 簡化閥門開度估算 (20-100)

                    // 1. 使用標準參數設定

                    // 2. 根據補償模式調整速度和流量
                    if (currentCompensationMode === 'standard') {
                        // 標準模式下，速度和流量使用固定值或滑桿基礎值
                        actualRobotSpeed = parseFloat(document.getElementById('max-speed').value);
                        const standardFlowPercent = parseFloat(document.getElementById('flow-stage1').value) || 20; // 從UI獲取第一段流量百分比
                        actualFlowRate = (standardFlowPercent / 100) * MAX_SCREW_VALVE_FLOW_CM3_S; // 轉換為絕對流量
                        actualValveOpening = standardFlowPercent; // 標準模式下閥門開度直接對應第一段流量百分比
                    }
                    // PID 模式下，actualRobotSpeed 和 actualFlowRate 已經由 speedResult 和 flowResult 以及可能的優化參數設定


                    robot.targetSpeed = actualRobotSpeed; // 設定機器人最終的目標速度
 
                    // --- 根據補償模式計算 compensatedHeight (第一階段：基於誤差和PID) ---
                    let baseCompensatedHeight;
                    if (currentCompensationMode === 'standard') {
                        // 標準模式：補償後高度 = 原始表面高度 + UI設定的目標膠高 (視為固定膠厚)
                        // 這將使得補償後的曲線形狀類似於原始表面曲線，只是整體抬高。
                        const targetHeightFromUI = parseFloat(document.getElementById('target-height').value) || 1.2;
                        baseCompensatedHeight = targetPoint.height + targetHeightFromUI;
                    } else if (currentCompensationMode === 'pid') {
                        // 改進的PID模式：確保能夠達到目標管制線範圍
                        // 1. 基礎補償：直接補償大部分誤差
                        const baseCompensation = errorForCompensation * 0.9; // 90%的基礎補償

                        // 2. PID精細調整：處理剩餘誤差和動態響應
                        const pidFineAdjustment = pidHeightContribution;

                        // 3. 組合補償，確保達到目標範圍
                        baseCompensatedHeight = targetPoint.height + baseCompensation + pidFineAdjustment;

                        // 4. 額外檢查：如果仍未達到目標公差，增加補償力度
                        const preliminaryError = Math.abs(baseCompensatedHeight - targetHeight);
                        const tolerance = parseFloat(document.getElementById('tolerance').value) || 0.2; // 從UI獲取公差設定

                        if (preliminaryError > tolerance) {
                            // 如果超出公差，增加額外補償
                            const additionalCompensation = (targetHeight - baseCompensatedHeight) * 0.8;
                            baseCompensatedHeight += additionalCompensation;
                        }
                    } else {
                        baseCompensatedHeight = targetPoint.height + errorForCompensation * 0.5;
                    }

                    // --- 第二階段：疊加流量和速度對高度的直接影響 ---
                    let heightAdjustmentFromProcessParams = 0;
                    const nominalMaxSpeed = parseFloat(document.getElementById('max-speed').value) || 20; // 從UI獲取名義最大速度
                    const nominalFlowStage1Percent = parseFloat(document.getElementById('flow-stage1').value) || 20; // 從UI獲取名義第一段流量百分比 (預設20%)
                    const nominalFlowStage1Absolute = (nominalFlowStage1Percent / 100) * MAX_SCREW_VALVE_FLOW_CM3_S; // 轉換為絕對流量
                    
                    // 確保 nominalMaxSpeed, actualRobotSpeed 和 nominalFlowStage1Absolute 不為零或過小，以避免除以零或無意義的計算
                    if (nominalMaxSpeed > 0.01 && actualRobotSpeed > 0.01 && nominalFlowStage1Absolute > 0.001) {
                        const nominalFlowPerSpeedRatio = nominalFlowStage1Absolute / nominalMaxSpeed;
                        const currentFlowPerSpeedRatio = actualFlowRate / actualRobotSpeed;
                        heightAdjustmentFromProcessParams = FLOW_SPEED_TO_HEIGHT_FACTOR * (currentFlowPerSpeedRatio - nominalFlowPerSpeedRatio);
                        
                        // 增加日誌以觀察此調整量
                        if (currentPoint % 10 === 0) { // 每10個點記錄一次
                           // addLog(`H_Adj_Proc: Point ${currentPoint}, actualF/S: ${(currentFlowPerSpeedRatio).toFixed(3)}, nominalF/S: ${nominalFlowPerSpeedRatio.toFixed(3)}, adj: ${heightAdjustmentFromProcessParams.toFixed(3)}, nomFlowAbs: ${nominalFlowStage1Absolute.toFixed(3)}`, 'debug');
                        }
                    } else if (currentPoint % 10 === 0) { // 如果條件不滿足，也記錄一下原因
                        // addLog(`H_Adj_Proc: Skipped. nomMaxSpeed: ${nominalMaxSpeed.toFixed(2)}, actRobSpeed: ${actualRobotSpeed.toFixed(2)}, nomFlowAbs: ${nominalFlowStage1Absolute.toFixed(3)}`, 'debug');
                    }
                    
                    let compensatedHeight = baseCompensatedHeight + heightAdjustmentFromProcessParams;
                    // --- 高度計算結束 ---
 
                    // 更新補償後高度的物理限制
                    const minAllowedGlueHeight = 0.0; // 假設膠水高度不能為負或太小
                    const maxAllowedGlueHeight = (targetHeight || 1.2) + 0.5; // 控制目標高度加上一些餘量
                    targetPoint.compensatedHeight = Math.max(minAllowedGlueHeight, Math.min(maxAllowedGlueHeight, compensatedHeight));
                    targetPoint.isCompensated = true;

                    // 檢查補償效果是否達到目標管制線
                    const finalError = Math.abs(targetPoint.compensatedHeight - targetHeight);
                    const tolerance = parseFloat(document.getElementById('tolerance').value) || 0.2; // 從UI獲取公差設定
                    targetPoint.withinTolerance = finalError <= tolerance;
                    targetPoint.finalError = finalError;
 
                    targetPoint.actualControlParams = {
                        robotSpeed: robot.targetSpeed,
                        flowRate: actualFlowRate,
                        valveOpening: actualValveOpening, // 這仍然是一個簡化估算
                        compensationFactor: currentCompensationMode === 'pid' ? actualCompensationFactor : (currentCompensationMode === 'standard' ? 0.8 : null)
                    };
 
                    currentPoint++;
                    if (currentPoint < scanData.length) {
                        // 檢查補償效果
                        const toleranceStatus = targetPoint.withinTolerance ? '✅達標' : '❌超差';
                        const errorInfo = `誤差${targetPoint.finalError.toFixed(3)}mm`;

                        addLog(`到達點 ${currentPoint}/${scanData.length}，原始: ${targetPoint.height.toFixed(3)}mm → 補償後: ${targetPoint.compensatedHeight.toFixed(3)}mm (${toleranceStatus}, ${errorInfo})，流量段: ${flowResult.stage}`, 'info');
                    }
                }

                // 繪製視覺化
                drawHeightMap();
                drawPIDResponse();
                drawRobotPath();
                drawProfileChart(); // 新增：動畫循環中也繪製剖面圖

                // 如果數據表格分頁是活動的，更新表格
                if (document.getElementById('height-data-view').style.display !== 'none') {
                    updateHeightDataTable();
                }
 
                animationId = requestAnimationFrame(animationLoop);
            } else {
                // 完成作業
                addLog('邊緣塗膠作業完成！', 'success');
                stopDispensing();
            }
        }

        // 事件監聽器設置
        function setupEventListeners() {
            // 參數滑桿事件
            const sliders = ['kp', 'ki', 'kd', 'target-height', 'target-width', 'tolerance',
                           'max-speed', 'acceleration']; // 移除流量滑桿，單獨處理

            sliders.forEach(id => {
                const slider = document.getElementById(id);
                const valueDisplay = document.getElementById(id + '-value');
                if (!slider) return; // 防禦性程式碼

                slider.addEventListener('input', (e) => {
                    if (valueDisplay) valueDisplay.textContent = e.target.value;
 
                    // 即時更新目標高度
                    if (id === 'target-height' && scanData.length > 0) {
                        const newTarget = parseFloat(e.target.value);
                        scanData.forEach(point => {
                            point.targetHeight = newTarget;
                        });
                        drawHeightMap();
                    }
                });
            });

            // 螺桿閥開度滑桿事件 (新的百分比滑桿)
            const flowSliders = ['flow-stage1', 'flow-stage2', 'flow-stage3'];
            flowSliders.forEach(id => {
                const slider = document.getElementById(id);
                const valueDisplay = document.getElementById(id + '-value');
                if (!slider || !valueDisplay) return;

                slider.addEventListener('input', (e) => {
                    valueDisplay.textContent = e.target.value; // 直接顯示百分比值
                });
            });
 
            // 按鈕事件
            document.getElementById('scan-btn').addEventListener('click', startScanning);
            document.getElementById('start-btn').addEventListener('click', startDispensing);
            document.getElementById('pause-btn').addEventListener('click', pauseDispensing);
            document.getElementById('stop-btn').addEventListener('click', stopDispensing);

            // 補償模式選擇事件
            const compensationModeRadios = document.querySelectorAll('input[name="compensation-mode"]');

            compensationModeRadios.forEach(radio => {
                radio.addEventListener('change', (e) => {
                    currentCompensationMode = e.target.value;
                    addLog(`補償模式切換為: ${currentCompensationMode}`, 'info');
                });
            });

            // 剖面圖滑鼠事件
            const profileCanvas = document.getElementById('profile-canvas');
            profileCanvas.addEventListener('mousemove', handleProfileMouseMove);
            profileCanvas.addEventListener('mouseleave', handleProfileMouseLeave);
        }

        // 剖面圖滑鼠事件處理函數
        function handleProfileMouseMove(event) {
            const rect = event.target.getBoundingClientRect();
            mouseX = event.clientX - rect.left;
            mouseY = event.clientY - rect.top;

            // 檢查是否有數據點在滑鼠附近
            if (scanData.length > 0) {
                const padding = 40;
                const chartWidth = event.target.width - 2 * padding;
                const pointWidth = chartWidth / (scanData.length - 1);

                // 計算最接近的點位索引
                const pointIndex = Math.round((mouseX - padding) / pointWidth);

                if (pointIndex >= 0 && pointIndex < scanData.length) {
                    const point = scanData[pointIndex];
                    const targetHeight = parseFloat(document.getElementById('target-height').value) || 1.2;
                    const tolerance = parseFloat(document.getElementById('tolerance').value) || 0.2;

                    // 檢查是否有補償後高度
                    if (point.compensatedHeight !== null) {
                        const finalError = Math.abs(point.compensatedHeight - targetHeight);
                        const withinTolerance = finalError <= tolerance;

                        tooltipData = {
                            pointIndex: pointIndex + 1,
                            originalHeight: point.height,
                            compensatedHeight: point.compensatedHeight,
                            withinTolerance: withinTolerance,
                            error: finalError
                        };
                        showTooltip = true;
                    } else {
                        tooltipData = {
                            pointIndex: pointIndex + 1,
                            originalHeight: point.height,
                            compensatedHeight: null,
                            withinTolerance: null,
                            error: null
                        };
                        showTooltip = true;
                    }

                    // 重新繪製圖表以顯示tooltip
                    drawProfileChart();
                }
            }
        }

        function handleProfileMouseLeave() {
            showTooltip = false;
            drawProfileChart(); // 重新繪製以隱藏tooltip
        }

        // 分頁切換函數
        function switchHeightTab(tabName) {
            // 隱藏所有分頁內容
            document.getElementById('height-map-view').style.display = 'none';
            document.getElementById('height-data-view').style.display = 'none';

            // 移除所有標籤的active類
            document.getElementById('height-map-tab').classList.remove('active');
            document.getElementById('height-data-tab').classList.remove('active');

            // 顯示選中的分頁內容
            if (tabName === 'map') {
                document.getElementById('height-map-view').style.display = 'block';
                document.getElementById('height-map-tab').classList.add('active');
            } else if (tabName === 'data') {
                document.getElementById('height-data-view').style.display = 'block';
                document.getElementById('height-data-tab').classList.add('active');
                updateHeightDataTable(); // 更新數據表格
            }
        }

        // 更新高度數據表格
        function updateHeightDataTable() {
            const tbody = document.getElementById('height-data-tbody');

            if (scanData.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6" style="text-align: center; padding: 20px; color: #666;">請先進行表面掃描</td></tr>';
                return;
            }

            let html = '';
            scanData.forEach((point, index) => {
                const compensatedHeight = point.compensatedHeight !== null ? point.compensatedHeight.toFixed(3) : '-';
                const status = point.isCompensated ?
                    (point.withinTolerance ? '✅已補償(達標)' : '❌已補償(超差)') :
                    '⏳待處理';

                const statusColor = point.isCompensated ?
                    (point.withinTolerance ? '#28a745' : '#dc3545') :
                    '#6c757d';

                html += `
                    <tr>
                        <td>${index + 1}</td>
                        <td>${point.x.toFixed(1)}</td>
                        <td>${point.y.toFixed(1)}</td>
                        <td>${point.height.toFixed(3)}</td>
                        <td>${compensatedHeight}</td>
                        <td style="color: ${statusColor}; font-weight: bold;">${status}</td>
                    </tr>
                `;
            });

            tbody.innerHTML = html;
        }

        // 初始化系統
        function initializeSystem() {
            addLog('BLU邊緣塗膠PID控制模擬系統啟動', 'success');
            addLog('系統版本: v1.0.0', 'info');
            addLog('請點擊"開始掃描"來掃描BLU表面高度', 'info');

            setupEventListeners();

            // 初始化畫布
            drawHeightMap();
            drawPIDResponse();
            drawRobotPath();
            drawProfileChart(); // 新增：初始化時繪製剖面圖
        }
 
        // 頁面載入完成後初始化
        document.addEventListener('DOMContentLoaded', initializeSystem);
    </script>
</body>
</html>