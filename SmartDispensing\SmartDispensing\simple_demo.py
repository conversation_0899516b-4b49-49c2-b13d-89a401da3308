#!/usr/bin/env python3
"""
簡化的BLU邊緣塗膠PID控制演示
展示核心功能而不依賴複雜的GUI
"""

import numpy as np
import matplotlib.pyplot as plt
import time
import logging
import platform

# 設置中文字體
def setup_chinese_font():
    """設置matplotlib中文字體"""
    system = platform.system()

    if system == "Windows":
        # Windows系統常用中文字體
        chinese_fonts = ['Microsoft YaHei', 'SimHei', 'SimSun', 'KaiTi']
    elif system == "Darwin":  # macOS
        chinese_fonts = ['PingFang SC', 'Heiti SC', 'STHeiti', 'Arial Unicode MS']
    else:  # Linux
        chinese_fonts = ['WenQuanYi Micro Hei', 'DejaVu Sans', 'Liberation Sans']

    # 嘗試設置中文字體
    for font_name in chinese_fonts:
        try:
            plt.rcParams['font.sans-serif'] = [font_name]
            plt.rcParams['axes.unicode_minus'] = False  # 解決負號顯示問題
            print(f"✅ 成功設置字體: {font_name}")
            return True
        except:
            continue

    # 如果都失敗，使用默認設置
    print("⚠️ 無法找到中文字體，使用默認字體")
    plt.rcParams['axes.unicode_minus'] = False
    return False

# 初始化字體設置
setup_chinese_font()

# 配置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SimplePIDController:
    """簡化的PID控制器"""
    
    def __init__(self, kp=1.2, ki=0.1, kd=0.05):
        self.kp = kp
        self.ki = ki
        self.kd = kd
        self.previous_error = 0
        self.integral = 0
        self.last_time = time.time()
    
    def update(self, setpoint, current_value):
        """更新PID控制器"""
        current_time = time.time()
        delta_time = current_time - self.last_time
        
        if delta_time <= 0:
            delta_time = 0.001
        
        error = setpoint - current_value
        
        # PID計算
        proportional = self.kp * error
        self.integral += error * delta_time
        integral_term = self.ki * self.integral
        derivative = self.kd * (error - self.previous_error) / delta_time
        
        output = proportional + integral_term + derivative
        
        self.previous_error = error
        self.last_time = current_time
        
        return {
            'output': output,
            'error': error,
            'proportional': proportional,
            'integral': integral_term,
            'derivative': derivative
        }

class BLUSimpleDemo:
    """簡化的BLU演示系統"""
    
    def __init__(self):
        self.scan_points = 50  # 減少點數以加快演示
        self.target_height = 1.2
        self.pid_controller = SimplePIDController()
        
        # 生成模擬數據
        self.generate_data()
        
        print("🚀 BLU邊緣塗膠PID控制簡化演示")
        print("=" * 50)
    
    def generate_data(self):
        """生成模擬BLU表面數據"""
        print("🔍 掃描BLU表面...")
        
        self.surface_data = []
        for i in range(self.scan_points):
            angle = (i / self.scan_points) * 2 * np.pi
            
            # 模擬表面高度變化
            base_height = 1.0
            variation = 0.3 * np.sin(angle * 3) + 0.2 * np.cos(angle * 5)
            noise = (np.random.random() - 0.5) * 0.1
            height = max(0.5, min(1.5, base_height + variation + noise))
            
            self.surface_data.append({
                'angle': angle,
                'height': height,
                'x': 100 * np.cos(angle),
                'y': 100 * np.sin(angle)
            })
        
        heights = [p['height'] for p in self.surface_data]
        print(f"✅ 掃描完成！高度範圍: {min(heights):.3f}-{max(heights):.3f}mm")
    
    def calculate_flow_stage(self, height_error):
        """計算塗膠流量段數"""
        abs_error = abs(height_error)
        
        if abs_error > 0.15:
            return 3, 0.8  # 第3段，高流量
        elif abs_error > 0.08:
            return 2, 0.6  # 第2段，中流量
        else:
            return 1, 0.4  # 第1段，低流量
    
    def run_simulation(self):
        """運行模擬"""
        print("▶️  開始邊緣塗膠作業...")
        
        # 數據記錄
        pid_history = []
        flow_history = []
        position_history = []
        
        # 模擬塗膠過程
        for i, point in enumerate(self.surface_data):
            current_height = point['height']
            height_error = self.target_height - current_height
            
            # PID控制計算
            pid_result = self.pid_controller.update(self.target_height, current_height)
            
            # 計算機器人速度（基於PID輸出）
            base_speed = 20.0  # mm/s
            speed_adjustment = abs(pid_result['output']) * 0.5
            robot_speed = max(5.0, base_speed - speed_adjustment)
            
            # 計算塗膠流量
            flow_stage, flow_rate = self.calculate_flow_stage(height_error)
            
            # 記錄數據
            pid_history.append(pid_result)
            flow_history.append({'stage': flow_stage, 'flow': flow_rate})
            position_history.append({'x': point['x'], 'y': point['y'], 'height': current_height})
            
            # 顯示進度
            if i % 10 == 0:
                progress = (i + 1) / len(self.surface_data) * 100
                print(f"🎯 進度: {progress:.1f}% | "
                      f"位置: ({point['x']:.1f}, {point['y']:.1f}) | "
                      f"誤差: {height_error:.3f}mm | "
                      f"速度: {robot_speed:.1f}mm/s | "
                      f"流量段: {flow_stage}")
        
        print("🎉 塗膠作業完成！")
        
        # 生成視覺化報告
        self.generate_report(pid_history, flow_history, position_history)
    
    def generate_report(self, pid_history, flow_history, position_history):
        """生成視覺化報告"""
        print("\n📊 生成視覺化報告...")
        
        # 創建圖表
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('BLU邊緣塗膠PID控制執行報告', fontsize=16, fontweight='bold')
        
        # 1. 表面高度分佈
        ax1 = axes[0, 0]
        angles = [p['angle'] for p in self.surface_data]
        heights = [p['height'] for p in self.surface_data]
        
        ax1.set_title('🔍 BLU表面高度分佈')
        scatter = ax1.scatter(angles, heights, c=heights, cmap='coolwarm', s=50)
        ax1.set_xlabel('角度 (rad)')
        ax1.set_ylabel('高度 (mm)')
        plt.colorbar(scatter, ax=ax1, label='高度 (mm)')
        ax1.grid(True, alpha=0.3)
        
        # 2. PID控制響應
        ax2 = axes[0, 1]
        errors = [p['error'] for p in pid_history]
        outputs = [p['output'] for p in pid_history]
        
        ax2.set_title('🎛️ PID控制響應')
        ax2.plot(errors, 'r-', label='高度誤差', linewidth=2)
        ax2.plot(outputs, 'b-', label='PID輸出', linewidth=2)
        ax2.axhline(y=0, color='k', linestyle='--', alpha=0.5)
        ax2.set_xlabel('測量點')
        ax2.set_ylabel('數值')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 3. 機器人路徑
        ax3 = axes[1, 0]
        x_coords = [p['x'] for p in position_history]
        y_coords = [p['y'] for p in position_history]
        heights_path = [p['height'] for p in position_history]
        
        ax3.set_title('🤖 機器人移動路徑')
        path_scatter = ax3.scatter(x_coords, y_coords, c=heights_path, cmap='coolwarm', s=30)
        ax3.plot(x_coords, y_coords, 'k-', alpha=0.3, linewidth=1)
        ax3.set_xlabel('X (mm)')
        ax3.set_ylabel('Y (mm)')
        ax3.set_aspect('equal')
        plt.colorbar(path_scatter, ax=ax3, label='高度 (mm)')
        ax3.grid(True, alpha=0.3)
        
        # 4. 流量控制統計
        ax4 = axes[1, 1]
        stages = [f['stage'] for f in flow_history]
        flows = [f['flow'] for f in flow_history]
        
        ax4.set_title('💧 塗膠流量控制')
        ax4.plot(flows, 'g-', linewidth=2, label='流量')
        
        # 標示不同段數
        stage_colors = {1: 'lightblue', 2: 'orange', 3: 'red'}
        for i, stage in enumerate(stages):
            ax4.axvline(x=i, color=stage_colors[stage], alpha=0.3)
        
        ax4.set_xlabel('測量點')
        ax4.set_ylabel('流量 (cm³/s)')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存圖表
        plt.savefig('blu_pid_control_report.png', dpi=300, bbox_inches='tight')
        print("📈 報告已保存為 'blu_pid_control_report.png'")
        
        # 顯示統計結果
        self.print_statistics(pid_history, flow_history)
        
        # 顯示圖表
        plt.show()
    
    def print_statistics(self, pid_history, flow_history):
        """打印統計結果"""
        print("\n📊 執行統計結果:")
        print("=" * 40)
        
        # PID性能統計
        errors = [abs(p['error']) for p in pid_history]
        outputs = [abs(p['output']) for p in pid_history]
        
        print(f"🎛️  PID控制性能:")
        print(f"   • 平均絕對誤差: {np.mean(errors):.3f}mm")
        print(f"   • 最大絕對誤差: {np.max(errors):.3f}mm")
        print(f"   • 平均PID輸出: {np.mean(outputs):.3f}")
        print(f"   • 最大PID輸出: {np.max(outputs):.3f}")
        
        # 流量控制統計
        stages = [f['stage'] for f in flow_history]
        stage_counts = {1: stages.count(1), 2: stages.count(2), 3: stages.count(3)}
        
        print(f"\n💧 流量控制統計:")
        print(f"   • 第1段使用: {stage_counts[1]}次 ({stage_counts[1]/len(stages)*100:.1f}%)")
        print(f"   • 第2段使用: {stage_counts[2]}次 ({stage_counts[2]/len(stages)*100:.1f}%)")
        print(f"   • 第3段使用: {stage_counts[3]}次 ({stage_counts[3]/len(stages)*100:.1f}%)")
        
        # 品質評估
        within_tolerance = sum(1 for e in errors if e <= 0.2)  # 公差±0.2mm
        quality_rate = within_tolerance / len(errors) * 100
        
        print(f"\n✅ 品質評估:")
        print(f"   • 公差內點數: {within_tolerance}/{len(errors)}")
        print(f"   • 品質合格率: {quality_rate:.1f}%")
        
        if quality_rate >= 95:
            print("   • 評級: 優秀 🌟")
        elif quality_rate >= 90:
            print("   • 評級: 良好 👍")
        elif quality_rate >= 80:
            print("   • 評級: 尚可 ⚠️")
        else:
            print("   • 評級: 需改善 ❌")
        
        print("=" * 40)

def main():
    """主函數"""
    try:
        demo = BLUSimpleDemo()
        demo.run_simulation()
        
        print("\n👋 演示完成！感謝使用BLU邊緣塗膠PID控制系統")
        
    except KeyboardInterrupt:
        print("\n⏹️  演示已停止")
    except Exception as e:
        print(f"❌ 演示失敗: {e}")
        logger.error(f"演示執行失敗: {e}")

if __name__ == "__main__":
    main()
