"""
BLU高度檢測與補償算法模組
實現精確的高度測量、表面分析和膠水補償計算
"""

import numpy as np
import cv2
from scipy import interpolate, ndimage
from scipy.spatial import distance
import logging
from typing import Dict, List, Tuple, Optional, Union
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class SurfaceAnalysisResult:
    """表面分析結果"""
    mean_height: float
    height_range: float
    roughness: float
    slope_variance: float
    critical_areas: List[Tuple[float, float, float]]  # (x, y, height_deficit)

@dataclass
class CompensationPlan:
    """補償計劃"""
    compensation_map: np.ndarray
    total_volume_needed: float
    max_compensation: float
    min_compensation: float
    quality_score: float

class HeightDetectionSystem:
    """高度檢測系統"""
    
    def __init__(self, measurement_resolution: float = 0.01):
        """
        初始化高度檢測系統
        
        Args:
            measurement_resolution: 測量解析度 (mm)
        """
        self.resolution = measurement_resolution
        self.calibration_data = {}
        self.noise_threshold = 0.005  # 噪聲閾值 (mm)
        
    def process_laser_scan_data(self, raw_data: np.ndarray, scan_params: Dict) -> np.ndarray:
        """
        處理激光掃描數據
        
        Args:
            raw_data: 原始掃描數據
            scan_params: 掃描參數
            
        Returns:
            處理後的高度數據
        """
        try:
            # 去噪處理
            filtered_data = self._apply_noise_filter(raw_data)
            
            # 校準處理
            calibrated_data = self._apply_calibration(filtered_data, scan_params)
            
            # 平滑處理
            smoothed_data = self._apply_smoothing(calibrated_data)
            
            logger.info(f"激光掃描數據處理完成，數據點數: {len(smoothed_data)}")
            return smoothed_data
            
        except Exception as e:
            logger.error(f"激光掃描數據處理失敗: {e}")
            raise
    
    def _apply_noise_filter(self, data: np.ndarray) -> np.ndarray:
        """應用噪聲濾波器"""
        # 使用中值濾波去除突發噪聲
        filtered = ndimage.median_filter(data, size=3)
        
        # 使用高斯濾波平滑數據
        gaussian_filtered = ndimage.gaussian_filter1d(filtered, sigma=1.0)
        
        return gaussian_filtered
    
    def _apply_calibration(self, data: np.ndarray, params: Dict) -> np.ndarray:
        """應用校準參數"""
        # 簡化的線性校準
        scale_factor = params.get('scale_factor', 1.0)
        offset = params.get('offset', 0.0)
        
        calibrated = data * scale_factor + offset
        return calibrated
    
    def _apply_smoothing(self, data: np.ndarray) -> np.ndarray:
        """應用平滑處理"""
        # 使用Savitzky-Golay濾波器進行平滑
        from scipy.signal import savgol_filter
        
        if len(data) > 5:
            smoothed = savgol_filter(data, window_length=5, polyorder=2)
        else:
            smoothed = data
            
        return smoothed

class SurfaceAnalyzer:
    """表面分析器"""
    
    def __init__(self, target_flatness: float = 0.05):
        """
        初始化表面分析器
        
        Args:
            target_flatness: 目標平整度 (mm)
        """
        self.target_flatness = target_flatness
    
    def analyze_surface_topology(self, height_map: np.ndarray, coordinates: np.ndarray) -> SurfaceAnalysisResult:
        """
        分析表面拓撲結構
        
        Args:
            height_map: 高度圖
            coordinates: 座標數據
            
        Returns:
            表面分析結果
        """
        try:
            # 基本統計分析
            mean_height = np.mean(height_map)
            height_range = np.max(height_map) - np.min(height_map)
            
            # 計算表面粗糙度
            roughness = self._calculate_roughness(height_map)
            
            # 計算斜率變化
            slope_variance = self._calculate_slope_variance(height_map, coordinates)
            
            # 識別關鍵區域（需要重點補償的區域）
            critical_areas = self._identify_critical_areas(height_map, coordinates, mean_height)
            
            result = SurfaceAnalysisResult(
                mean_height=mean_height,
                height_range=height_range,
                roughness=roughness,
                slope_variance=slope_variance,
                critical_areas=critical_areas
            )
            
            logger.info(f"表面分析完成 - 平均高度: {mean_height:.3f}mm, "
                       f"高度範圍: {height_range:.3f}mm, "
                       f"粗糙度: {roughness:.3f}mm")
            
            return result
            
        except Exception as e:
            logger.error(f"表面分析失敗: {e}")
            raise
    
    def _calculate_roughness(self, height_map: np.ndarray) -> float:
        """計算表面粗糙度 (Ra)"""
        mean_height = np.mean(height_map)
        roughness = np.mean(np.abs(height_map - mean_height))
        return roughness
    
    def _calculate_slope_variance(self, height_map: np.ndarray, coordinates: np.ndarray) -> float:
        """計算斜率變化"""
        if len(coordinates) < 3:
            return 0.0
        
        # 計算相鄰點之間的斜率
        slopes = []
        for i in range(len(coordinates) - 1):
            dx = coordinates[i+1, 0] - coordinates[i, 0]
            dy = coordinates[i+1, 1] - coordinates[i, 1]
            dh = height_map[i+1] - height_map[i]
            
            if dx != 0 or dy != 0:
                distance_2d = np.sqrt(dx**2 + dy**2)
                slope = dh / distance_2d
                slopes.append(slope)
        
        return np.var(slopes) if slopes else 0.0
    
    def _identify_critical_areas(self, height_map: np.ndarray, coordinates: np.ndarray, 
                               reference_height: float) -> List[Tuple[float, float, float]]:
        """識別需要重點補償的關鍵區域"""
        critical_areas = []
        threshold = self.target_flatness * 2  # 超過兩倍目標平整度的區域
        
        for i, (x, y) in enumerate(coordinates):
            height_deficit = reference_height - height_map[i]
            if height_deficit > threshold:
                critical_areas.append((x, y, height_deficit))
        
        return critical_areas

class CompensationCalculator:
    """補償計算器"""
    
    def __init__(self, adhesive_properties: Dict):
        """
        初始化補償計算器
        
        Args:
            adhesive_properties: 膠水特性參數
        """
        self.adhesive_density = adhesive_properties.get('density', 1.2)  # g/cm³
        self.adhesive_viscosity = adhesive_properties.get('viscosity', 5000)  # cP
        self.flow_characteristics = adhesive_properties.get('flow_factor', 0.8)
        self.cure_shrinkage = adhesive_properties.get('shrinkage', 0.02)  # 2%收縮
    
    def calculate_compensation_map(self, surface_analysis: SurfaceAnalysisResult, 
                                 target_height: float, edge_width: float) -> CompensationPlan:
        """
        計算補償映射
        
        Args:
            surface_analysis: 表面分析結果
            target_height: 目標高度
            edge_width: 邊緣寬度
            
        Returns:
            補償計劃
        """
        try:
            # 計算基礎補償量
            base_compensation = target_height - surface_analysis.mean_height
            
            # 為關鍵區域計算額外補償
            compensation_map = np.full(len(surface_analysis.critical_areas), base_compensation)
            
            for i, (x, y, deficit) in enumerate(surface_analysis.critical_areas):
                # 考慮膠水流動特性
                flow_compensation = deficit * self.flow_characteristics
                
                # 考慮固化收縮
                shrinkage_compensation = flow_compensation * (1 + self.cure_shrinkage)
                
                compensation_map[i] = shrinkage_compensation
            
            # 計算總體積需求
            total_volume = self._calculate_total_volume(compensation_map, edge_width)
            
            # 計算品質分數
            quality_score = self._calculate_quality_score(compensation_map, surface_analysis)
            
            plan = CompensationPlan(
                compensation_map=compensation_map,
                total_volume_needed=total_volume,
                max_compensation=np.max(compensation_map) if len(compensation_map) > 0 else 0,
                min_compensation=np.min(compensation_map) if len(compensation_map) > 0 else 0,
                quality_score=quality_score
            )
            
            logger.info(f"補償計劃生成完成 - 總體積需求: {total_volume:.3f}mm³, "
                       f"品質分數: {quality_score:.2f}")
            
            return plan
            
        except Exception as e:
            logger.error(f"補償計算失敗: {e}")
            raise
    
    def _calculate_total_volume(self, compensation_map: np.ndarray, edge_width: float) -> float:
        """計算總體積需求"""
        if len(compensation_map) == 0:
            return 0.0
        
        # 假設每個補償點覆蓋固定長度的邊緣
        segment_length = 1.0  # mm
        total_volume = np.sum(compensation_map) * edge_width * segment_length
        
        return total_volume
    
    def _calculate_quality_score(self, compensation_map: np.ndarray, 
                               surface_analysis: SurfaceAnalysisResult) -> float:
        """計算補償品質分數 (0-100)"""
        if len(compensation_map) == 0:
            return 100.0
        
        # 基於補償一致性和表面分析結果計算分數
        compensation_variance = np.var(compensation_map)
        consistency_score = max(0, 100 - compensation_variance * 1000)
        
        # 基於表面粗糙度的分數
        roughness_score = max(0, 100 - surface_analysis.roughness * 2000)
        
        # 綜合分數
        overall_score = (consistency_score + roughness_score) / 2
        
        return min(100.0, max(0.0, overall_score))
