"""
邊緣膠水塗佈控制模組
實現精確的邊緣膠水塗佈控制，寬度1.4mm，高度1.2mm，公差±0.2mm
"""

import numpy as np
import time
import logging
from typing import Dict, List, Tuple, Optional, Callable
from dataclasses import dataclass
from enum import Enum
import threading
from queue import Queue

logger = logging.getLogger(__name__)

class DispensingState(Enum):
    """點膠狀態"""
    IDLE = "idle"
    PREPARING = "preparing"
    DISPENSING = "dispensing"
    PAUSED = "paused"
    ERROR = "error"
    COMPLETED = "completed"

@dataclass
class DispensingParameters:
    """點膠參數"""
    flow_rate: float  # 流量 (ml/min)
    pressure: float   # 壓力 (bar)
    speed: float      # 移動速度 (mm/s)
    height_offset: float  # 高度偏移 (mm)
    temperature: float    # 溫度 (°C)

@dataclass
class QualityMetrics:
    """品質指標"""
    width_actual: float
    height_actual: float
    width_deviation: float
    height_deviation: float
    surface_flatness: float
    adhesion_quality: float

class EdgeCoatingController:
    """邊緣塗膠控制器"""
    
    def __init__(self, target_width: float = 1.4, target_height: float = 1.2, tolerance: float = 0.2):
        """
        初始化邊緣塗膠控制器
        
        Args:
            target_width: 目標寬度 (mm)
            target_height: 目標高度 (mm)
            tolerance: 公差 (±mm)
        """
        self.target_width = target_width
        self.target_height = target_height
        self.tolerance = tolerance
        
        self.state = DispensingState.IDLE
        self.current_position = (0.0, 0.0, 0.0)
        self.dispensing_queue = Queue()
        self.quality_data = []
        
        # 控制參數
        self.default_params = DispensingParameters(
            flow_rate=2.5,    # ml/min
            pressure=1.2,     # bar
            speed=10.0,       # mm/s
            height_offset=0.5, # mm
            temperature=25.0   # °C
        )
        
        # 回調函數
        self.position_callback: Optional[Callable] = None
        self.pressure_callback: Optional[Callable] = None
        self.flow_callback: Optional[Callable] = None
        
        logger.info(f"邊緣塗膠控制器初始化完成 - 目標: {target_width}×{target_height}mm, 公差: ±{tolerance}mm")
    
    def set_hardware_callbacks(self, position_cb: Callable, pressure_cb: Callable, flow_cb: Callable):
        """設置硬體控制回調函數"""
        self.position_callback = position_cb
        self.pressure_callback = pressure_cb
        self.flow_callback = flow_cb
        logger.info("硬體控制回調函數設置完成")
    
    def calculate_dispensing_parameters(self, compensation_factor: float, surface_roughness: float) -> DispensingParameters:
        """
        根據補償係數和表面粗糙度計算點膠參數
        
        Args:
            compensation_factor: 補償係數
            surface_roughness: 表面粗糙度
            
        Returns:
            優化的點膠參數
        """
        # 基於補償係數調整流量
        adjusted_flow_rate = self.default_params.flow_rate * compensation_factor
        
        # 基於表面粗糙度調整壓力
        pressure_adjustment = 1.0 + (surface_roughness * 10)  # 粗糙表面需要更高壓力
        adjusted_pressure = self.default_params.pressure * pressure_adjustment
        
        # 基於補償需求調整速度
        speed_factor = 1.0 / max(0.5, compensation_factor)  # 補償量大時速度慢
        adjusted_speed = self.default_params.speed * speed_factor
        
        # 確保參數在安全範圍內
        adjusted_flow_rate = np.clip(adjusted_flow_rate, 0.5, 10.0)
        adjusted_pressure = np.clip(adjusted_pressure, 0.5, 3.0)
        adjusted_speed = np.clip(adjusted_speed, 2.0, 50.0)
        
        params = DispensingParameters(
            flow_rate=adjusted_flow_rate,
            pressure=adjusted_pressure,
            speed=adjusted_speed,
            height_offset=self.default_params.height_offset,
            temperature=self.default_params.temperature
        )
        
        logger.debug(f"計算點膠參數 - 流量: {params.flow_rate:.2f}ml/min, "
                    f"壓力: {params.pressure:.2f}bar, 速度: {params.speed:.2f}mm/s")
        
        return params
    
    def execute_edge_coating(self, path_points: List[Tuple[float, float, float]], 
                           compensation_data: List[float]) -> bool:
        """
        執行邊緣塗膠
        
        Args:
            path_points: 路徑點列表 [(x, y, z), ...]
            compensation_data: 對應的補償數據
            
        Returns:
            執行是否成功
        """
        if len(path_points) != len(compensation_data):
            logger.error("路徑點數量與補償數據數量不匹配")
            return False
        
        try:
            self.state = DispensingState.PREPARING
            logger.info(f"開始邊緣塗膠，共{len(path_points)}個點")
            
            # 預熱和準備
            self._prepare_system()
            
            self.state = DispensingState.DISPENSING
            
            for i, ((x, y, z), compensation) in enumerate(zip(path_points, compensation_data)):
                if self.state != DispensingState.DISPENSING:
                    logger.warning("點膠過程被中斷")
                    break
                
                # 計算當前點的參數
                surface_roughness = 0.01  # 簡化，實際應從感測器獲取
                params = self.calculate_dispensing_parameters(compensation, surface_roughness)
                
                # 移動到目標位置
                success = self._move_to_position(x, y, z + params.height_offset)
                if not success:
                    logger.error(f"移動到位置 ({x}, {y}, {z}) 失敗")
                    self.state = DispensingState.ERROR
                    return False
                
                # 設置點膠參數
                self._set_dispensing_parameters(params)
                
                # 執行點膠
                quality = self._dispense_at_current_position(params)
                self.quality_data.append(quality)
                
                # 檢查品質
                if not self._validate_quality(quality):
                    logger.warning(f"第{i+1}點品質不符合要求")
                
                # 進度報告
                if (i + 1) % 10 == 0:
                    logger.info(f"點膠進度: {i+1}/{len(path_points)}")
            
            self.state = DispensingState.COMPLETED
            logger.info("邊緣塗膠完成")
            return True
            
        except Exception as e:
            logger.error(f"邊緣塗膠執行失敗: {e}")
            self.state = DispensingState.ERROR
            return False
    
    def _prepare_system(self):
        """準備系統"""
        logger.info("準備點膠系統...")
        
        # 設置初始溫度
        if self.flow_callback:
            self.flow_callback("set_temperature", self.default_params.temperature)
        
        # 預加壓
        if self.pressure_callback:
            self.pressure_callback("set_pressure", self.default_params.pressure * 0.8)
        
        # 等待穩定
        time.sleep(2.0)
        
        logger.info("系統準備完成")
    
    def _move_to_position(self, x: float, y: float, z: float) -> bool:
        """移動到指定位置"""
        try:
            if self.position_callback:
                result = self.position_callback("move_to", x, y, z)
                if result:
                    self.current_position = (x, y, z)
                    return True
            return False
        except Exception as e:
            logger.error(f"位置移動失敗: {e}")
            return False
    
    def _set_dispensing_parameters(self, params: DispensingParameters):
        """設置點膠參數"""
        try:
            if self.pressure_callback:
                self.pressure_callback("set_pressure", params.pressure)
            
            if self.flow_callback:
                self.flow_callback("set_flow_rate", params.flow_rate)
                
        except Exception as e:
            logger.error(f"設置點膠參數失敗: {e}")
    
    def _dispense_at_current_position(self, params: DispensingParameters) -> QualityMetrics:
        """在當前位置執行點膠"""
        try:
            # 開始點膠
            if self.flow_callback:
                self.flow_callback("start_dispensing")
            
            # 計算點膠時間（基於目標體積和流量）
            target_volume = self.target_width * self.target_height * 1.0  # 假設1mm長度
            dispense_time = (target_volume / 1000) / (params.flow_rate / 60)  # 轉換單位
            
            # 等待點膠完成
            time.sleep(dispense_time)
            
            # 停止點膠
            if self.flow_callback:
                self.flow_callback("stop_dispensing")
            
            # 模擬品質測量（實際應從感測器獲取）
            width_actual = self.target_width + np.random.normal(0, 0.05)
            height_actual = self.target_height + np.random.normal(0, 0.03)
            
            quality = QualityMetrics(
                width_actual=width_actual,
                height_actual=height_actual,
                width_deviation=abs(width_actual - self.target_width),
                height_deviation=abs(height_actual - self.target_height),
                surface_flatness=np.random.uniform(0.01, 0.08),
                adhesion_quality=np.random.uniform(0.85, 0.98)
            )
            
            return quality
            
        except Exception as e:
            logger.error(f"點膠執行失敗: {e}")
            # 返回失敗的品質指標
            return QualityMetrics(0, 0, 999, 999, 999, 0)
    
    def _validate_quality(self, quality: QualityMetrics) -> bool:
        """驗證品質是否符合要求"""
        width_ok = quality.width_deviation <= self.tolerance
        height_ok = quality.height_deviation <= self.tolerance
        flatness_ok = quality.surface_flatness <= 0.05  # 表面平整度要求
        adhesion_ok = quality.adhesion_quality >= 0.8   # 黏附品質要求
        
        return width_ok and height_ok and flatness_ok and adhesion_ok
    
    def get_quality_report(self) -> Dict:
        """獲取品質報告"""
        if not self.quality_data:
            return {"error": "無品質數據"}
        
        width_deviations = [q.width_deviation for q in self.quality_data]
        height_deviations = [q.height_deviation for q in self.quality_data]
        flatness_values = [q.surface_flatness for q in self.quality_data]
        
        report = {
            "total_points": len(self.quality_data),
            "width_stats": {
                "mean_deviation": np.mean(width_deviations),
                "max_deviation": np.max(width_deviations),
                "within_tolerance": sum(1 for d in width_deviations if d <= self.tolerance)
            },
            "height_stats": {
                "mean_deviation": np.mean(height_deviations),
                "max_deviation": np.max(height_deviations),
                "within_tolerance": sum(1 for d in height_deviations if d <= self.tolerance)
            },
            "surface_flatness": {
                "mean": np.mean(flatness_values),
                "max": np.max(flatness_values),
                "acceptable_points": sum(1 for f in flatness_values if f <= 0.05)
            },
            "overall_pass_rate": sum(1 for q in self.quality_data if self._validate_quality(q)) / len(self.quality_data)
        }
        
        return report
    
    def emergency_stop(self):
        """緊急停止"""
        logger.warning("執行緊急停止")
        self.state = DispensingState.PAUSED
        
        # 停止所有運動和流量
        if self.flow_callback:
            self.flow_callback("emergency_stop")
        if self.position_callback:
            self.position_callback("emergency_stop")
        if self.pressure_callback:
            self.pressure_callback("set_pressure", 0)
