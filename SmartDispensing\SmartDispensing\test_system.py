"""
BLU邊緣塗膠系統測試腳本
驗證各個模組的基本功能
"""

import numpy as np
import logging
import time
import sys
import os

# 添加src目錄到路徑
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from core.dispensing_system import BLUEdgeDispensingSystem, DispensingSpec
from algorithms.height_detection import HeightDetectionSystem, SurfaceAnalyzer, CompensationCalculator
from control.edge_coating_controller import EdgeCoatingController
from monitoring.surface_monitor import SurfaceMonitor
from quality.tolerance_control import ToleranceController, ToleranceSpec

# 配置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_height_detection():
    """測試高度檢測模組"""
    logger.info("=== 測試高度檢測模組 ===")
    
    # 創建測試數據
    test_data = np.random.normal(1.0, 0.1, 100)  # 模擬高度數據
    scan_params = {'scale_factor': 1.0, 'offset': 0.0}
    
    # 初始化系統
    detector = HeightDetectionSystem(measurement_resolution=0.01)
    
    # 處理數據
    processed_data = detector.process_laser_scan_data(test_data, scan_params)
    
    logger.info(f"原始數據點數: {len(test_data)}")
    logger.info(f"處理後數據點數: {len(processed_data)}")
    logger.info(f"數據範圍: {np.min(processed_data):.3f} - {np.max(processed_data):.3f}mm")
    
    return len(processed_data) > 0

def test_surface_analysis():
    """測試表面分析模組"""
    logger.info("=== 測試表面分析模組 ===")
    
    # 創建測試數據
    x = np.linspace(0, 10, 50)
    y = np.linspace(0, 10, 50)
    xx, yy = np.meshgrid(x, y)
    coordinates = np.column_stack([xx.ravel(), yy.ravel()])
    
    # 模擬有起伏的表面
    height_map = 1.0 + 0.2 * np.sin(xx) * np.cos(yy) + np.random.normal(0, 0.01, xx.shape)
    height_map = height_map.ravel()
    
    # 初始化分析器
    analyzer = SurfaceAnalyzer(target_flatness=0.05)
    
    # 執行分析
    result = analyzer.analyze_surface_topology(height_map, coordinates)
    
    logger.info(f"平均高度: {result.mean_height:.3f}mm")
    logger.info(f"高度範圍: {result.height_range:.3f}mm")
    logger.info(f"表面粗糙度: {result.roughness:.3f}mm")
    logger.info(f"關鍵區域數量: {len(result.critical_areas)}")
    
    return result.mean_height > 0

def test_compensation_calculation():
    """測試補償計算模組"""
    logger.info("=== 測試補償計算模組 ===")
    
    # 模擬表面分析結果
    from algorithms.height_detection import SurfaceAnalysisResult
    
    surface_analysis = SurfaceAnalysisResult(
        mean_height=1.0,
        height_range=0.3,
        roughness=0.02,
        slope_variance=0.001,
        critical_areas=[(5.0, 5.0, 0.15), (8.0, 3.0, 0.12)]  # (x, y, height_deficit)
    )
    
    # 初始化計算器
    adhesive_props = {
        'density': 1.2,
        'viscosity': 5000,
        'flow_factor': 0.8,
        'shrinkage': 0.02
    }
    calculator = CompensationCalculator(adhesive_props)
    
    # 計算補償
    plan = calculator.calculate_compensation_map(surface_analysis, target_height=1.2, edge_width=1.4)
    
    logger.info(f"總體積需求: {plan.total_volume_needed:.3f}mm³")
    logger.info(f"最大補償: {plan.max_compensation:.3f}mm")
    logger.info(f"品質分數: {plan.quality_score:.2f}")
    
    return plan.total_volume_needed > 0

def test_dispensing_system():
    """測試點膠系統"""
    logger.info("=== 測試點膠系統 ===")
    
    # 創建規格
    spec = DispensingSpec(width=1.4, height=1.2, tolerance=0.2)
    
    # 初始化系統
    system = BLUEdgeDispensingSystem(spec)
    
    # 創建測試高度數據
    x = np.linspace(0, 20, 40)
    y = np.linspace(0, 15, 30)
    xx, yy = np.meshgrid(x, y)
    coordinates = np.column_stack([xx.ravel(), yy.ravel()])
    
    # 模擬BLU底部起伏
    center_x, center_y = 10, 7.5
    distance = np.sqrt((xx - center_x)**2 + (yy - center_y)**2)
    height_data = 0.8 + 0.3 * np.exp(-distance / 5) + np.random.normal(0, 0.02, xx.shape)
    height_data = height_data.ravel()
    
    # 載入數據
    success = system.load_height_profile(height_data, coordinates)
    if not success:
        logger.error("載入高度輪廓失敗")
        return False
    
    # 計算補償
    success = system.calculate_compensation_algorithm()
    if not success:
        logger.error("補償算法計算失敗")
        return False
    
    # 驗證平整度
    flatness_metrics = system.validate_surface_flatness()
    
    # 獲取點膠計劃
    plan = system.get_dispensing_plan()
    
    logger.info(f"點膠點數量: {plan['total_points']}")
    logger.info(f"表面平整度符合要求: {flatness_metrics['meets_requirement']}")
    logger.info(f"最大偏差: {flatness_metrics['max_deviation']:.3f}mm")
    
    return plan['total_points'] > 0

def test_edge_coating_controller():
    """測試邊緣塗膠控制器"""
    logger.info("=== 測試邊緣塗膠控制器 ===")
    
    # 初始化控制器
    controller = EdgeCoatingController(target_width=1.4, target_height=1.2, tolerance=0.2)
    
    # 設置模擬回調函數
    def mock_position_callback(command, *args):
        return True
    
    def mock_pressure_callback(command, *args):
        return True
    
    def mock_flow_callback(command, *args):
        return True
    
    controller.set_hardware_callbacks(
        mock_position_callback, mock_pressure_callback, mock_flow_callback
    )
    
    # 創建測試路徑
    path_points = [(i, 0, 1.0) for i in range(10)]  # 10個點的直線路徑
    compensation_data = [1.0 + 0.1 * np.sin(i) for i in range(10)]  # 模擬補償數據
    
    # 執行塗膠
    success = controller.execute_edge_coating(path_points, compensation_data)
    
    # 獲取品質報告
    quality_report = controller.get_quality_report()
    
    logger.info(f"塗膠執行成功: {success}")
    logger.info(f"總點數: {quality_report['total_points']}")
    logger.info(f"整體通過率: {quality_report['overall_pass_rate']:.2%}")
    
    return success

def test_tolerance_control():
    """測試公差控制"""
    logger.info("=== 測試公差控制 ===")
    
    # 創建規格
    spec = ToleranceSpec(target_width=1.4, target_height=1.2, tolerance=0.2)
    
    # 初始化控制器
    controller = ToleranceController(spec)
    
    # 模擬圖像數據
    test_image = np.random.randint(0, 255, (480, 640), dtype=np.uint8)
    
    # 執行尺寸測量
    measurements = controller.measure_dimensions(test_image, calibration_factor=100.0)
    
    # 模擬高度數據進行平整度測量
    height_map = np.random.normal(1.2, 0.02, 100)
    flatness_result = controller.evaluate_surface_flatness(height_map)
    
    measurements.append(flatness_result)
    
    # 生成品質報告
    report = controller.generate_quality_report("TEST_001", measurements)
    
    logger.info(f"測量項目數: {len(measurements)}")
    logger.info(f"整體狀態: {report.overall_status.value}")
    logger.info(f"通過率: {report.statistics.get('pass_rate', 0):.2%}")
    
    return len(measurements) > 0

def run_all_tests():
    """執行所有測試"""
    logger.info("開始執行BLU邊緣塗膠系統測試")
    
    tests = [
        ("高度檢測", test_height_detection),
        ("表面分析", test_surface_analysis),
        ("補償計算", test_compensation_calculation),
        ("點膠系統", test_dispensing_system),
        ("邊緣塗膠控制", test_edge_coating_controller),
        ("公差控制", test_tolerance_control)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            logger.info(f"\n開始測試: {test_name}")
            start_time = time.time()
            
            result = test_func()
            
            elapsed_time = time.time() - start_time
            results[test_name] = {
                'success': result,
                'time': elapsed_time
            }
            
            status = "通過" if result else "失敗"
            logger.info(f"{test_name}測試{status} (耗時: {elapsed_time:.2f}秒)")
            
        except Exception as e:
            logger.error(f"{test_name}測試發生錯誤: {e}")
            results[test_name] = {
                'success': False,
                'error': str(e)
            }
    
    # 輸出測試摘要
    logger.info("\n=== 測試結果摘要 ===")
    total_tests = len(tests)
    passed_tests = sum(1 for r in results.values() if r['success'])
    
    logger.info(f"總測試數: {total_tests}")
    logger.info(f"通過測試: {passed_tests}")
    logger.info(f"失敗測試: {total_tests - passed_tests}")
    logger.info(f"通過率: {passed_tests/total_tests:.1%}")
    
    for test_name, result in results.items():
        status = "✓" if result['success'] else "✗"
        time_info = f"({result['time']:.2f}s)" if 'time' in result else ""
        error_info = f" - {result['error']}" if 'error' in result else ""
        logger.info(f"  {status} {test_name} {time_info}{error_info}")
    
    return passed_tests == total_tests

if __name__ == "__main__":
    success = run_all_tests()
    
    if success:
        logger.info("\n🎉 所有測試通過！系統運行正常。")
        sys.exit(0)
    else:
        logger.error("\n❌ 部分測試失敗，請檢查系統配置。")
        sys.exit(1)
