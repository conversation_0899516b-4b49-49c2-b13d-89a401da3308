#!/usr/bin/env python3
"""
測試main.py的中文字體顯示
"""

import sys
import os
import matplotlib.pyplot as plt
import numpy as np
import platform

# 添加src目錄到路徑
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# 設置中文字體
def setup_chinese_font():
    """設置matplotlib中文字體"""
    system = platform.system()
    print(f"檢測到系統: {system}")
    
    if system == "Windows":
        # Windows系統常用中文字體
        chinese_fonts = ['Microsoft YaHei', 'SimHei', 'SimSun', 'KaiTi']
    elif system == "Darwin":  # macOS
        chinese_fonts = ['PingFang SC', 'Heiti SC', 'STHeiti', 'Arial Unicode MS']
    else:  # Linux
        chinese_fonts = ['WenQuanYi Micro Hei', 'DejaVu Sans', 'Liberation Sans']
    
    # 嘗試設置中文字體
    for font_name in chinese_fonts:
        try:
            plt.rcParams['font.sans-serif'] = [font_name]
            plt.rcParams['axes.unicode_minus'] = False  # 解決負號顯示問題
            print(f"✅ 成功設置字體: {font_name}")
            return font_name
        except Exception as e:
            print(f"❌ 字體 {font_name} 設置失敗: {e}")
            continue
    
    # 如果都失敗，使用默認設置
    print("⚠️ 無法找到中文字體，使用默認字體")
    plt.rcParams['axes.unicode_minus'] = False
    return "默認字體"

def test_visualization_system():
    """測試視覺化系統的中文顯示"""
    print("🧪 測試BLU邊緣塗膠視覺化系統中文顯示...")
    
    # 設置字體
    font_name = setup_chinese_font()
    
    # 模擬BLU邊緣塗膠數據
    scan_points = 50
    height_data = []
    pid_history = []
    flow_history = []
    
    # 生成模擬數據
    for i in range(scan_points):
        angle = (i / scan_points) * 2 * np.pi
        
        # 模擬表面高度變化
        base_height = 1.0
        variation = 0.3 * np.sin(angle * 3) + 0.2 * np.cos(angle * 5)
        noise = (np.random.random() - 0.5) * 0.1
        height = max(0.5, min(1.5, base_height + variation + noise))
        
        # 計算笛卡爾座標
        radius = 100
        x = radius * np.cos(angle)
        y = radius * np.sin(angle)
        
        height_data.append({
            'index': i,
            'angle': angle,
            'x': x,
            'y': y,
            'height': height,
            'target_height': 1.2
        })
        
        # 模擬PID數據
        target_height = 1.2
        height_error = target_height - height
        pid_output = 1.2 * height_error + 0.1 * height_error * 0.1 + 0.05 * height_error
        
        pid_history.append({
            'time': i * 0.1,
            'output': pid_output,
            'error': height_error,
            'proportional': 1.2 * height_error,
            'integral': 0.1 * height_error * 0.1,
            'derivative': 0.05 * height_error
        })
        
        # 模擬流量數據
        abs_error = abs(height_error)
        if abs_error > 0.15:
            flow_stage = 3
            flow_rate = 0.8
        elif abs_error > 0.08:
            flow_stage = 2
            flow_rate = 0.6
        else:
            flow_stage = 1
            flow_rate = 0.4
        
        flow_history.append({
            'time': i * 0.1,
            'flow': flow_rate,
            'stage': flow_stage
        })
    
    # 創建視覺化圖表
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle(f'BLU邊緣塗膠PID控制即時監控系統 (字體: {font_name})', fontsize=16, fontweight='bold')
    
    # 1. BLU表面高度分佈
    ax1 = axes[0, 0]
    angles = [point['angle'] for point in height_data]
    heights = [point['height'] for point in height_data]
    
    # 使用極座標
    ax1.remove()
    ax1 = fig.add_subplot(2, 2, 1, projection='polar')
    scatter = ax1.scatter(angles, heights, c=heights, cmap='coolwarm', s=50)
    ax1.set_title('BLU表面高度分佈', fontsize=12, pad=20)
    ax1.set_ylim(0.5, 1.5)
    
    try:
        cbar1 = fig.colorbar(scatter, ax=ax1, shrink=0.8)
        cbar1.set_label('高度 (mm)', fontsize=10)
    except:
        pass
    
    # 2. PID控制響應
    ax2 = axes[0, 1]
    times = [d['time'] for d in pid_history]
    outputs = [d['output'] for d in pid_history]
    errors = [d['error'] for d in pid_history]
    
    ax2.plot(times, outputs, 'b-', linewidth=2, label='PID輸出')
    ax2.plot(times, errors, 'r-', linewidth=2, label='高度誤差')
    ax2.axhline(y=0, color='k', linestyle='--', alpha=0.5)
    ax2.set_title('PID控制響應', fontsize=12, pad=20)
    ax2.set_xlabel('時間 (s)', fontsize=10)
    ax2.set_ylabel('數值', fontsize=10)
    ax2.legend(fontsize=9)
    ax2.grid(True, alpha=0.3)
    
    # 3. 機器人移動路徑
    ax3 = axes[1, 0]
    x_coords = [point['x'] for point in height_data]
    y_coords = [point['y'] for point in height_data]
    heights_path = [point['height'] for point in height_data]
    
    path_scatter = ax3.scatter(x_coords, y_coords, c=heights_path, cmap='coolwarm', s=30)
    ax3.plot(x_coords, y_coords, 'k-', alpha=0.3, linewidth=1)
    ax3.set_title('機器人移動路徑', fontsize=12, pad=20)
    ax3.set_xlabel('X (mm)', fontsize=10)
    ax3.set_ylabel('Y (mm)', fontsize=10)
    ax3.set_aspect('equal')
    ax3.grid(True, alpha=0.3)
    
    try:
        cbar3 = fig.colorbar(path_scatter, ax=ax3, shrink=0.8)
        cbar3.set_label('高度 (mm)', fontsize=10)
    except:
        pass
    
    # 4. 塗膠流量控制
    ax4 = axes[1, 1]
    flow_times = [f['time'] for f in flow_history]
    flows = [f['flow'] for f in flow_history]
    stages = [f['stage'] for f in flow_history]
    
    ax4.plot(flow_times, flows, 'g-', linewidth=2, label='流量')
    
    # 標示不同段數
    stage_colors = {1: 'lightblue', 2: 'orange', 3: 'red'}
    for i, stage in enumerate(stages):
        if i < len(flow_times):
            ax4.axvline(x=flow_times[i], color=stage_colors[stage], alpha=0.3)
    
    ax4.set_title('三段式流量控制', fontsize=12, pad=20)
    ax4.set_xlabel('時間 (s)', fontsize=10)
    ax4.set_ylabel('流量 (cm³/s)', fontsize=10)
    ax4.legend(fontsize=9)
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存圖表
    plt.savefig('main_chinese_test.png', dpi=300, bbox_inches='tight')
    print("📊 測試圖表已保存為 'main_chinese_test.png'")
    
    # 顯示統計結果
    print("\n📊 模擬執行統計結果:")
    print("=" * 40)
    
    # PID性能統計
    errors_abs = [abs(p['error']) for p in pid_history]
    outputs_abs = [abs(p['output']) for p in pid_history]
    
    print(f"PID控制性能:")
    print(f"   • 平均絕對誤差: {np.mean(errors_abs):.3f}mm")
    print(f"   • 最大絕對誤差: {np.max(errors_abs):.3f}mm")
    print(f"   • 平均PID輸出: {np.mean(outputs_abs):.3f}")
    print(f"   • 最大PID輸出: {np.max(outputs_abs):.3f}")
    
    # 流量控制統計
    stage_counts = {1: stages.count(1), 2: stages.count(2), 3: stages.count(3)}
    
    print(f"\n流量控制統計:")
    print(f"   • 第1段使用: {stage_counts[1]}次 ({stage_counts[1]/len(stages)*100:.1f}%)")
    print(f"   • 第2段使用: {stage_counts[2]}次 ({stage_counts[2]/len(stages)*100:.1f}%)")
    print(f"   • 第3段使用: {stage_counts[3]}次 ({stage_counts[3]/len(stages)*100:.1f}%)")
    
    # 品質評估
    within_tolerance = sum(1 for e in errors_abs if e <= 0.2)  # 公差±0.2mm
    quality_rate = within_tolerance / len(errors_abs) * 100
    
    print(f"\n品質評估:")
    print(f"   • 公差內點數: {within_tolerance}/{len(errors_abs)}")
    print(f"   • 品質合格率: {quality_rate:.1f}%")
    
    if quality_rate >= 95:
        print("   • 評級: 優秀")
    elif quality_rate >= 90:
        print("   • 評級: 良好")
    elif quality_rate >= 80:
        print("   • 評級: 尚可")
    else:
        print("   • 評級: 需改善")
    
    print("=" * 40)
    
    # 顯示圖表
    plt.show()
    
    return True

def main():
    """主函數"""
    print("🧪 BLU邊緣塗膠系統 - main.py中文字體測試")
    print("=" * 50)
    
    try:
        success = test_visualization_system()
        if success:
            print("\n✅ 中文字體測試完成！")
            print("📋 測試結果:")
            print("   ✅ 中文標題顯示正常")
            print("   ✅ 軸標籤顯示正常")
            print("   ✅ 圖例顯示正常")
            print("   ✅ 數據統計正常")
        else:
            print("\n❌ 測試失敗")
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
