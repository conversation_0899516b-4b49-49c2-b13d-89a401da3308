#!/usr/bin/env python3
"""
測試中文字體顯示
"""

import matplotlib.pyplot as plt
import numpy as np
import platform

def setup_chinese_font():
    """設置matplotlib中文字體"""
    system = platform.system()
    print(f"檢測到系統: {system}")
    
    if system == "Windows":
        # Windows系統常用中文字體
        chinese_fonts = ['Microsoft YaHei', 'SimHei', 'SimSun', 'KaiTi']
    elif system == "Darwin":  # macOS
        chinese_fonts = ['PingFang SC', 'Heiti SC', 'STHeiti', 'Arial Unicode MS']
    else:  # Linux
        chinese_fonts = ['WenQuanYi Micro Hei', 'DejaVu Sans', 'Liberation Sans']
    
    # 嘗試設置中文字體
    for font_name in chinese_fonts:
        try:
            plt.rcParams['font.sans-serif'] = [font_name]
            plt.rcParams['axes.unicode_minus'] = False  # 解決負號顯示問題
            print(f"✅ 成功設置字體: {font_name}")
            return font_name
        except Exception as e:
            print(f"❌ 字體 {font_name} 設置失敗: {e}")
            continue
    
    # 如果都失敗，使用默認設置
    print("⚠️ 無法找到中文字體，使用默認字體")
    plt.rcParams['axes.unicode_minus'] = False
    return "默認字體"

def test_chinese_display():
    """測試中文顯示"""
    print("🧪 開始測試中文字體顯示...")
    
    # 設置字體
    font_name = setup_chinese_font()
    
    # 創建測試圖表
    fig, axes = plt.subplots(2, 2, figsize=(12, 8))
    fig.suptitle(f'BLU邊緣塗膠PID控制系統 - 中文字體測試 (使用字體: {font_name})', fontsize=14, fontweight='bold')
    
    # 測試數據
    x = np.linspace(0, 10, 100)
    y1 = np.sin(x)
    y2 = np.cos(x)
    
    # 子圖1: 基本中文標題
    ax1 = axes[0, 0]
    ax1.plot(x, y1, 'b-', linewidth=2, label='正弦波')
    ax1.set_title('🔍 BLU表面高度分佈', fontsize=12, pad=15)
    ax1.set_xlabel('時間 (秒)', fontsize=10)
    ax1.set_ylabel('高度 (毫米)', fontsize=10)
    ax1.legend(fontsize=9)
    ax1.grid(True, alpha=0.3)
    
    # 子圖2: PID控制響應
    ax2 = axes[0, 1]
    ax2.plot(x, y2, 'r-', linewidth=2, label='PID輸出')
    ax2.plot(x, y1*0.5, 'g--', linewidth=2, label='誤差信號')
    ax2.set_title('🎛️ PID控制響應', fontsize=12, pad=15)
    ax2.set_xlabel('時間 (秒)', fontsize=10)
    ax2.set_ylabel('控制量', fontsize=10)
    ax2.legend(fontsize=9)
    ax2.grid(True, alpha=0.3)
    
    # 子圖3: 機器人路徑
    ax3 = axes[1, 0]
    theta = np.linspace(0, 2*np.pi, 50)
    r = 1 + 0.3*np.sin(3*theta)
    x_robot = r * np.cos(theta)
    y_robot = r * np.sin(theta)
    ax3.plot(x_robot, y_robot, 'g-', linewidth=2, label='機器人路徑')
    ax3.scatter(x_robot[::5], y_robot[::5], c='red', s=30, label='測量點')
    ax3.set_title('🤖 機器人移動路徑', fontsize=12, pad=15)
    ax3.set_xlabel('X座標 (毫米)', fontsize=10)
    ax3.set_ylabel('Y座標 (毫米)', fontsize=10)
    ax3.set_aspect('equal')
    ax3.legend(fontsize=9)
    ax3.grid(True, alpha=0.3)
    
    # 子圖4: 流量控制
    ax4 = axes[1, 1]
    flow_data = np.random.uniform(0.2, 1.0, 50)
    stages = np.random.choice([1, 2, 3], 50)
    colors = ['lightblue', 'orange', 'red']
    
    for i, stage in enumerate([1, 2, 3]):
        mask = stages == stage
        if np.any(mask):
            ax4.scatter(np.where(mask)[0], flow_data[mask], 
                       c=colors[i], s=30, label=f'第{stage}段流量', alpha=0.7)
    
    ax4.set_title('💧 三段式流量控制', fontsize=12, pad=15)
    ax4.set_xlabel('測量點編號', fontsize=10)
    ax4.set_ylabel('流量 (立方厘米/秒)', fontsize=10)
    ax4.legend(fontsize=9)
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存圖片
    plt.savefig('chinese_font_test.png', dpi=300, bbox_inches='tight')
    print("📊 測試圖表已保存為 'chinese_font_test.png'")
    
    # 顯示圖表
    plt.show()
    
    # 測試結果
    print("\n📋 中文字體測試結果:")
    print("=" * 40)
    print(f"✅ 使用字體: {font_name}")
    print("✅ 圖表標題: BLU邊緣塗膠PID控制系統")
    print("✅ 軸標籤: 時間(秒), 高度(毫米), 控制量")
    print("✅ 圖例: 正弦波, PID輸出, 誤差信號")
    print("✅ 特殊符號: 🔍🎛️🤖💧")
    print("=" * 40)
    
    if font_name != "默認字體":
        print("🎉 中文字體設置成功！")
    else:
        print("⚠️ 使用默認字體，可能無法正確顯示中文")

def main():
    """主函數"""
    print("🧪 BLU邊緣塗膠系統 - 中文字體測試")
    print("=" * 50)
    
    try:
        test_chinese_display()
        print("\n✅ 測試完成！")
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")

if __name__ == "__main__":
    main()
