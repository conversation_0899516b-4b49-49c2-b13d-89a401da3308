"""
公差控制與品質檢測系統
確保BLU邊緣塗膠產品符合規格要求：寬度1.4mm，高度1.2mm，公差±0.2mm
"""

import numpy as np
import cv2
import logging
from typing import Dict, List, Tuple, Optional, Union
from dataclasses import dataclass
from enum import Enum
import json
import time
from pathlib import Path

logger = logging.getLogger(__name__)

class QualityStatus(Enum):
    """品質狀態"""
    PASS = "pass"
    FAIL = "fail"
    WARNING = "warning"
    PENDING = "pending"

class MeasurementType(Enum):
    """測量類型"""
    WIDTH = "width"
    HEIGHT = "height"
    FLATNESS = "flatness"
    ADHESION = "adhesion"
    OVERALL = "overall"

@dataclass
class ToleranceSpec:
    """公差規格"""
    target_width: float = 1.4      # 目標寬度 (mm)
    target_height: float = 1.2     # 目標高度 (mm)
    tolerance: float = 0.2         # 公差 (±mm)
    flatness_requirement: float = 0.05  # 平整度要求 (mm)
    adhesion_strength_min: float = 0.8  # 最小黏附強度

@dataclass
class MeasurementResult:
    """測量結果"""
    measurement_type: MeasurementType
    measured_value: float
    target_value: float
    tolerance: float
    deviation: float
    status: QualityStatus
    timestamp: float
    location: Optional[Tuple[float, float]] = None

@dataclass
class QualityReport:
    """品質報告"""
    sample_id: str
    timestamp: float
    overall_status: QualityStatus
    measurements: List[MeasurementResult]
    statistics: Dict
    recommendations: List[str]

class ToleranceController:
    """公差控制器"""
    
    def __init__(self, spec: ToleranceSpec):
        """
        初始化公差控制器
        
        Args:
            spec: 公差規格
        """
        self.spec = spec
        self.measurement_history = []
        self.calibration_data = {}
        
        # 統計控制參數
        self.control_limits = {
            'width': {
                'ucl': spec.target_width + spec.tolerance,  # 上控制限
                'lcl': spec.target_width - spec.tolerance,  # 下控制限
                'usl': spec.target_width + spec.tolerance,  # 上規格限
                'lsl': spec.target_width - spec.tolerance   # 下規格限
            },
            'height': {
                'ucl': spec.target_height + spec.tolerance,
                'lcl': spec.target_height - spec.tolerance,
                'usl': spec.target_height + spec.tolerance,
                'lsl': spec.target_height - spec.tolerance
            }
        }
        
        logger.info(f"公差控制器初始化完成 - 目標: {spec.target_width}×{spec.target_height}mm, 公差: ±{spec.tolerance}mm")
    
    def measure_dimensions(self, image_data: np.ndarray, calibration_factor: float = 1.0) -> List[MeasurementResult]:
        """
        測量尺寸
        
        Args:
            image_data: 圖像數據
            calibration_factor: 校準係數 (pixel/mm)
            
        Returns:
            測量結果列表
        """
        try:
            results = []
            timestamp = time.time()
            
            # 圖像預處理
            processed_image = self._preprocess_image(image_data)
            
            # 檢測膠水邊緣
            contours = self._detect_adhesive_edges(processed_image)
            
            if not contours:
                logger.warning("未檢測到膠水邊緣")
                return []
            
            # 選擇最大輪廓（假設為主要膠水區域）
            main_contour = max(contours, key=cv2.contourArea)
            
            # 測量寬度
            width_result = self._measure_width(main_contour, calibration_factor, timestamp)
            results.append(width_result)
            
            # 測量高度
            height_result = self._measure_height(main_contour, calibration_factor, timestamp)
            results.append(height_result)
            
            # 存儲測量歷史
            self.measurement_history.extend(results)
            
            logger.info(f"尺寸測量完成 - 寬度: {width_result.measured_value:.3f}mm, "
                       f"高度: {height_result.measured_value:.3f}mm")
            
            return results
            
        except Exception as e:
            logger.error(f"尺寸測量失敗: {e}")
            return []
    
    def _preprocess_image(self, image: np.ndarray) -> np.ndarray:
        """圖像預處理"""
        # 轉換為灰度圖
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()
        
        # 高斯模糊
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)
        
        # 自適應閾值
        thresh = cv2.adaptiveThreshold(blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                                     cv2.THRESH_BINARY, 11, 2)
        
        return thresh
    
    def _detect_adhesive_edges(self, processed_image: np.ndarray) -> List:
        """檢測膠水邊緣"""
        # 邊緣檢測
        edges = cv2.Canny(processed_image, 50, 150)
        
        # 形態學操作
        kernel = np.ones((3, 3), np.uint8)
        edges = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel)
        
        # 尋找輪廓
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # 過濾小輪廓
        min_area = 100  # 最小面積閾值
        filtered_contours = [c for c in contours if cv2.contourArea(c) > min_area]
        
        return filtered_contours
    
    def _measure_width(self, contour: np.ndarray, calibration_factor: float, timestamp: float) -> MeasurementResult:
        """測量寬度"""
        # 計算邊界矩形
        rect = cv2.minAreaRect(contour)
        width_pixels = min(rect[1])  # 取較小的邊作為寬度
        
        # 轉換為實際尺寸
        width_mm = width_pixels / calibration_factor
        
        # 計算偏差
        deviation = abs(width_mm - self.spec.target_width)
        
        # 判斷狀態
        if deviation <= self.spec.tolerance:
            status = QualityStatus.PASS
        elif deviation <= self.spec.tolerance * 1.5:
            status = QualityStatus.WARNING
        else:
            status = QualityStatus.FAIL
        
        return MeasurementResult(
            measurement_type=MeasurementType.WIDTH,
            measured_value=width_mm,
            target_value=self.spec.target_width,
            tolerance=self.spec.tolerance,
            deviation=deviation,
            status=status,
            timestamp=timestamp
        )
    
    def _measure_height(self, contour: np.ndarray, calibration_factor: float, timestamp: float) -> MeasurementResult:
        """測量高度"""
        # 計算邊界矩形
        rect = cv2.minAreaRect(contour)
        height_pixels = max(rect[1])  # 取較大的邊作為高度
        
        # 轉換為實際尺寸
        height_mm = height_pixels / calibration_factor
        
        # 計算偏差
        deviation = abs(height_mm - self.spec.target_height)
        
        # 判斷狀態
        if deviation <= self.spec.tolerance:
            status = QualityStatus.PASS
        elif deviation <= self.spec.tolerance * 1.5:
            status = QualityStatus.WARNING
        else:
            status = QualityStatus.FAIL
        
        return MeasurementResult(
            measurement_type=MeasurementType.HEIGHT,
            measured_value=height_mm,
            target_value=self.spec.target_height,
            tolerance=self.spec.tolerance,
            deviation=deviation,
            status=status,
            timestamp=timestamp
        )
    
    def evaluate_surface_flatness(self, height_map: np.ndarray) -> MeasurementResult:
        """評估表面平整度"""
        timestamp = time.time()
        
        # 計算平整度（最大高度差）
        flatness = np.max(height_map) - np.min(height_map)
        
        # 計算偏差
        deviation = abs(flatness - 0.0)  # 理想平整度為0
        
        # 判斷狀態
        if flatness <= self.spec.flatness_requirement:
            status = QualityStatus.PASS
        elif flatness <= self.spec.flatness_requirement * 2:
            status = QualityStatus.WARNING
        else:
            status = QualityStatus.FAIL
        
        result = MeasurementResult(
            measurement_type=MeasurementType.FLATNESS,
            measured_value=flatness,
            target_value=0.0,
            tolerance=self.spec.flatness_requirement,
            deviation=deviation,
            status=status,
            timestamp=timestamp
        )
        
        self.measurement_history.append(result)
        return result
    
    def calculate_process_capability(self, measurement_type: MeasurementType, 
                                   sample_size: int = 30) -> Dict:
        """計算製程能力指標"""
        # 獲取最近的測量數據
        recent_measurements = [m for m in self.measurement_history[-sample_size:] 
                             if m.measurement_type == measurement_type]
        
        if len(recent_measurements) < 10:
            return {"error": "樣本數量不足"}
        
        values = [m.measured_value for m in recent_measurements]
        mean = np.mean(values)
        std = np.std(values, ddof=1)
        
        # 獲取規格限
        if measurement_type == MeasurementType.WIDTH:
            usl = self.control_limits['width']['usl']
            lsl = self.control_limits['width']['lsl']
            target = self.spec.target_width
        elif measurement_type == MeasurementType.HEIGHT:
            usl = self.control_limits['height']['usl']
            lsl = self.control_limits['height']['lsl']
            target = self.spec.target_height
        else:
            return {"error": "不支援的測量類型"}
        
        # 計算能力指標
        cp = (usl - lsl) / (6 * std) if std > 0 else float('inf')
        cpk = min((usl - mean) / (3 * std), (mean - lsl) / (3 * std)) if std > 0 else float('inf')
        
        capability = {
            "measurement_type": measurement_type.value,
            "sample_size": len(recent_measurements),
            "mean": mean,
            "std": std,
            "cp": cp,
            "cpk": cpk,
            "usl": usl,
            "lsl": lsl,
            "target": target,
            "capability_assessment": self._assess_capability(cp, cpk)
        }
        
        return capability
    
    def _assess_capability(self, cp: float, cpk: float) -> str:
        """評估製程能力"""
        if cpk >= 1.33:
            return "優秀"
        elif cpk >= 1.0:
            return "良好"
        elif cpk >= 0.67:
            return "尚可"
        else:
            return "不足"
    
    def generate_quality_report(self, sample_id: str, measurements: List[MeasurementResult]) -> QualityReport:
        """生成品質報告"""
        timestamp = time.time()
        
        # 判斷整體狀態
        statuses = [m.status for m in measurements]
        if all(s == QualityStatus.PASS for s in statuses):
            overall_status = QualityStatus.PASS
        elif any(s == QualityStatus.FAIL for s in statuses):
            overall_status = QualityStatus.FAIL
        else:
            overall_status = QualityStatus.WARNING
        
        # 計算統計數據
        statistics = self._calculate_statistics(measurements)
        
        # 生成建議
        recommendations = self._generate_recommendations(measurements)
        
        report = QualityReport(
            sample_id=sample_id,
            timestamp=timestamp,
            overall_status=overall_status,
            measurements=measurements,
            statistics=statistics,
            recommendations=recommendations
        )
        
        return report
    
    def _calculate_statistics(self, measurements: List[MeasurementResult]) -> Dict:
        """計算統計數據"""
        if not measurements:
            return {}
        
        deviations = [m.deviation for m in measurements]
        
        statistics = {
            "total_measurements": len(measurements),
            "pass_count": sum(1 for m in measurements if m.status == QualityStatus.PASS),
            "fail_count": sum(1 for m in measurements if m.status == QualityStatus.FAIL),
            "warning_count": sum(1 for m in measurements if m.status == QualityStatus.WARNING),
            "mean_deviation": np.mean(deviations),
            "max_deviation": np.max(deviations),
            "pass_rate": sum(1 for m in measurements if m.status == QualityStatus.PASS) / len(measurements)
        }
        
        return statistics
    
    def _generate_recommendations(self, measurements: List[MeasurementResult]) -> List[str]:
        """生成改善建議"""
        recommendations = []
        
        # 檢查寬度問題
        width_measurements = [m for m in measurements if m.measurement_type == MeasurementType.WIDTH]
        if width_measurements:
            avg_width_deviation = np.mean([m.deviation for m in width_measurements])
            if avg_width_deviation > self.spec.tolerance * 0.8:
                recommendations.append("建議調整點膠流量以改善寬度控制")
        
        # 檢查高度問題
        height_measurements = [m for m in measurements if m.measurement_type == MeasurementType.HEIGHT]
        if height_measurements:
            avg_height_deviation = np.mean([m.deviation for m in height_measurements])
            if avg_height_deviation > self.spec.tolerance * 0.8:
                recommendations.append("建議調整點膠壓力以改善高度控制")
        
        # 檢查平整度問題
        flatness_measurements = [m for m in measurements if m.measurement_type == MeasurementType.FLATNESS]
        if flatness_measurements:
            if any(m.status != QualityStatus.PASS for m in flatness_measurements):
                recommendations.append("建議檢查表面補償算法和點膠路徑規劃")
        
        # 整體建議
        fail_rate = sum(1 for m in measurements if m.status == QualityStatus.FAIL) / len(measurements)
        if fail_rate > 0.1:  # 失敗率超過10%
            recommendations.append("建議進行設備校準和製程參數優化")
        
        return recommendations
    
    def export_report(self, report: QualityReport, file_path: str):
        """匯出報告"""
        try:
            report_data = {
                "sample_id": report.sample_id,
                "timestamp": report.timestamp,
                "overall_status": report.overall_status.value,
                "measurements": [
                    {
                        "type": m.measurement_type.value,
                        "measured_value": m.measured_value,
                        "target_value": m.target_value,
                        "tolerance": m.tolerance,
                        "deviation": m.deviation,
                        "status": m.status.value,
                        "timestamp": m.timestamp
                    }
                    for m in report.measurements
                ],
                "statistics": report.statistics,
                "recommendations": report.recommendations
            }
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"品質報告已匯出至: {file_path}")
            
        except Exception as e:
            logger.error(f"匯出報告失敗: {e}")
