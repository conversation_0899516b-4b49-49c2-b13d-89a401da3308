#!/usr/bin/env python3
"""
BLU邊緣塗膠PID控制視覺化演示腳本
展示即時監控、PID控制和三段式流量控制
"""

import sys
import os
import time
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.animation as animation
from matplotlib.patches import Circle
import logging

# 添加src目錄到路徑
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# 配置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class BLUVisualizationDemo:
    """BLU邊緣塗膠視覺化演示"""
    
    def __init__(self):
        """初始化演示系統"""
        self.fig, self.axes = plt.subplots(2, 2, figsize=(15, 10))
        self.fig.suptitle('🤖 BLU邊緣塗膠PID控制即時監控系統', fontsize=16, fontweight='bold')
        
        # 數據初始化
        self.scan_points = 100
        self.current_point = 0
        self.height_data = []
        self.pid_history = []
        self.flow_history = []
        self.robot_path = []
        
        # PID參數
        self.kp = 1.2
        self.ki = 0.1
        self.kd = 0.05
        self.previous_error = 0
        self.integral = 0
        self.last_time = time.time()
        
        # 目標參數
        self.target_height = 1.2  # mm
        self.target_width = 1.4   # mm
        
        # 生成模擬數據
        self.generate_blu_surface_data()
        
        # 設置子圖
        self.setup_plots()
        
        print("🚀 BLU邊緣塗膠PID控制演示系統啟動")
        print("=" * 60)
        print("功能展示:")
        print("• 🔍 BLU表面高度掃描模擬")
        print("• 🎛️  PID控制算法即時響應")
        print("• 🤖 機器人路徑規劃與追蹤")
        print("• 💧 三段式塗膠流量控制")
        print("=" * 60)
    
    def generate_blu_surface_data(self):
        """生成BLU表面數據"""
        print("🔍 正在掃描BLU表面高度...")
        
        for i in range(self.scan_points):
            angle = (i / self.scan_points) * 2 * np.pi
            
            # 模擬BLU表面高度變化
            base_height = 1.0
            variation = 0.3 * np.sin(angle * 3) + 0.2 * np.cos(angle * 5)
            noise = (np.random.random() - 0.5) * 0.1
            height = base_height + variation + noise
            height = max(0.5, min(1.5, height))  # 限制範圍
            
            # 計算笛卡爾座標
            radius = 100
            x = radius * np.cos(angle)
            y = radius * np.sin(angle)
            
            self.height_data.append({
                'index': i,
                'angle': angle,
                'x': x,
                'y': y,
                'height': height,
                'target_height': self.target_height
            })
            
            self.robot_path.append((x, y))
        
        print(f"✅ 掃描完成！檢測到 {len(self.height_data)} 個測量點")
        
        # 分析掃描結果
        heights = [point['height'] for point in self.height_data]
        print(f"📊 高度範圍: {min(heights):.3f}mm - {max(heights):.3f}mm")
        print(f"📊 平均高度: {np.mean(heights):.3f}mm")
    
    def setup_plots(self):
        """設置子圖"""
        # 子圖1: BLU表面高度分佈
        self.ax1 = self.axes[0, 0]
        self.ax1.set_title('🔍 BLU表面高度分佈', fontweight='bold')
        self.ax1.set_xlabel('X (mm)')
        self.ax1.set_ylabel('Y (mm)')
        self.ax1.set_aspect('equal')
        self.ax1.grid(True, alpha=0.3)
        
        # 子圖2: PID控制響應
        self.ax2 = self.axes[0, 1]
        self.ax2.set_title('🎛️ PID控制響應', fontweight='bold')
        self.ax2.set_xlabel('時間 (s)')
        self.ax2.set_ylabel('數值')
        self.ax2.grid(True, alpha=0.3)
        
        # 子圖3: 機器人路徑追蹤
        self.ax3 = self.axes[1, 0]
        self.ax3.set_title('🤖 機器人路徑追蹤', fontweight='bold')
        self.ax3.set_xlabel('X (mm)')
        self.ax3.set_ylabel('Y (mm)')
        self.ax3.set_aspect('equal')
        self.ax3.grid(True, alpha=0.3)
        
        # 子圖4: 塗膠流量控制
        self.ax4 = self.axes[1, 1]
        self.ax4.set_title('💧 三段式流量控制', fontweight='bold')
        self.ax4.set_xlabel('時間 (s)')
        self.ax4.set_ylabel('流量 (cm³/s)')
        self.ax4.grid(True, alpha=0.3)
    
    def calculate_pid_output(self, setpoint, current_value):
        """計算PID輸出"""
        current_time = time.time()
        delta_time = current_time - self.last_time
        
        if delta_time <= 0:
            delta_time = 0.001
        
        error = setpoint - current_value
        
        # 比例項
        proportional = self.kp * error
        
        # 積分項
        self.integral += error * delta_time
        integral_term = self.ki * self.integral
        
        # 微分項
        derivative = self.kd * (error - self.previous_error) / delta_time
        
        # PID輸出
        output = proportional + integral_term + derivative
        
        self.previous_error = error
        self.last_time = current_time
        
        return {
            'output': output,
            'error': error,
            'proportional': proportional,
            'integral': integral_term,
            'derivative': derivative
        }
    
    def calculate_flow_stage(self, height_error):
        """計算塗膠流量段數"""
        abs_error = abs(height_error)
        
        if abs_error > 0.15:
            return 3, 0.8  # 第3段，高流量
        elif abs_error > 0.08:
            return 2, 0.6  # 第2段，中流量
        else:
            return 1, 0.4  # 第1段，低流量
    
    def update_animation(self, frame):
        """動畫更新函數"""
        if self.current_point >= len(self.height_data):
            return
        
        # 獲取當前點數據
        current_data = self.height_data[self.current_point]
        current_height = current_data['height']
        target_height = current_data['target_height']
        
        # 計算PID控制
        pid_result = self.calculate_pid_output(target_height, current_height)
        height_error = pid_result['error']
        
        # 計算流量控制
        flow_stage, flow_rate = self.calculate_flow_stage(height_error)
        
        # 記錄數據
        current_time = frame * 0.1  # 模擬時間
        self.pid_history.append({
            'time': current_time,
            'output': pid_result['output'],
            'error': height_error,
            'proportional': pid_result['proportional'],
            'integral': pid_result['integral'],
            'derivative': pid_result['derivative']
        })
        
        self.flow_history.append({
            'time': current_time,
            'flow': flow_rate,
            'stage': flow_stage
        })
        
        # 更新所有子圖
        self.update_height_plot()
        self.update_pid_plot()
        self.update_robot_plot()
        self.update_flow_plot()
        
        # 更新進度
        progress = (self.current_point + 1) / len(self.height_data) * 100
        
        # 在控制台顯示進度
        if self.current_point % 10 == 0:
            print(f"🎯 進度: {progress:.1f}% | "
                  f"位置: ({current_data['x']:.1f}, {current_data['y']:.1f}) | "
                  f"誤差: {height_error:.3f}mm | "
                  f"流量段: {flow_stage}")
        
        self.current_point += 1
        
        # 完成時顯示總結
        if self.current_point >= len(self.height_data):
            print("\n🎉 塗膠作業完成！")
            print("=" * 40)
            if self.pid_history:
                avg_error = np.mean([d['error'] for d in self.pid_history])
                max_output = np.max([abs(d['output']) for d in self.pid_history])
                print(f"📊 PID性能統計:")
                print(f"   • 平均誤差: {avg_error:.3f}mm")
                print(f"   • 最大輸出: {max_output:.3f}")
                print(f"   • 處理點數: {len(self.pid_history)}")
            print("=" * 40)
    
    def update_height_plot(self):
        """更新高度分佈圖"""
        self.ax1.clear()
        self.ax1.set_title('🔍 BLU表面高度分佈', fontweight='bold')
        self.ax1.set_xlabel('X (mm)')
        self.ax1.set_ylabel('Y (mm)')
        self.ax1.set_aspect('equal')
        self.ax1.grid(True, alpha=0.3)
        
        # 繪製BLU輪廓
        circle = Circle((0, 0), 100, fill=False, color='black', linewidth=2)
        self.ax1.add_patch(circle)
        
        # 繪製高度數據點
        for i, point in enumerate(self.height_data):
            # 顏色映射：藍色(低) -> 紅色(高)
            normalized_height = (point['height'] - 0.5) / 1.0
            color = plt.cm.coolwarm(normalized_height)
            
            marker_size = 30 if i == self.current_point else 20
            alpha = 1.0 if i <= self.current_point else 0.3
            
            self.ax1.scatter(point['x'], point['y'], c=[color], s=marker_size, alpha=alpha)
        
        # 標示當前位置
        if self.current_point < len(self.height_data):
            current = self.height_data[self.current_point]
            self.ax1.scatter(current['x'], current['y'], c='yellow', s=100, 
                           marker='*', edgecolors='black', linewidth=2)
    
    def update_pid_plot(self):
        """更新PID響應圖"""
        if len(self.pid_history) < 2:
            return
        
        self.ax2.clear()
        self.ax2.set_title('🎛️ PID控制響應', fontweight='bold')
        self.ax2.set_xlabel('時間 (s)')
        self.ax2.set_ylabel('數值')
        self.ax2.grid(True, alpha=0.3)
        
        times = [d['time'] for d in self.pid_history]
        outputs = [d['output'] for d in self.pid_history]
        errors = [d['error'] for d in self.pid_history]
        
        self.ax2.plot(times, outputs, 'b-', linewidth=2, label='PID輸出')
        self.ax2.plot(times, errors, 'r-', linewidth=2, label='高度誤差')
        self.ax2.axhline(y=0, color='k', linestyle='--', alpha=0.5)
        self.ax2.legend()
        
        # 顯示當前PID參數
        self.ax2.text(0.02, 0.98, f'Kp={self.kp}, Ki={self.ki}, Kd={self.kd}', 
                     transform=self.ax2.transAxes, verticalalignment='top',
                     bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    def update_robot_plot(self):
        """更新機器人路徑圖"""
        self.ax3.clear()
        self.ax3.set_title('🤖 機器人路徑追蹤', fontweight='bold')
        self.ax3.set_xlabel('X (mm)')
        self.ax3.set_ylabel('Y (mm)')
        self.ax3.set_aspect('equal')
        self.ax3.grid(True, alpha=0.3)
        
        # 繪製BLU輪廓
        circle = Circle((0, 0), 100, fill=False, color='black', linewidth=2)
        self.ax3.add_patch(circle)
        
        if self.current_point > 0:
            # 已完成路徑
            completed_x = [self.height_data[i]['x'] for i in range(self.current_point)]
            completed_y = [self.height_data[i]['y'] for i in range(self.current_point)]
            self.ax3.plot(completed_x, completed_y, 'g-', linewidth=3, label='已完成')
        
        if self.current_point < len(self.height_data):
            # 未完成路徑
            remaining_x = [self.height_data[i]['x'] for i in range(self.current_point, len(self.height_data))]
            remaining_y = [self.height_data[i]['y'] for i in range(self.current_point, len(self.height_data))]
            self.ax3.plot(remaining_x, remaining_y, 'gray', linestyle='--', alpha=0.5, label='待完成')
            
            # 當前位置
            current = self.height_data[self.current_point]
            self.ax3.plot(current['x'], current['y'], 'ro', markersize=10, label='當前位置')
        
        self.ax3.legend()
    
    def update_flow_plot(self):
        """更新流量控制圖"""
        if len(self.flow_history) < 2:
            return
        
        self.ax4.clear()
        self.ax4.set_title('💧 三段式流量控制', fontweight='bold')
        self.ax4.set_xlabel('時間 (s)')
        self.ax4.set_ylabel('流量 (cm³/s)')
        self.ax4.grid(True, alpha=0.3)
        
        times = [d['time'] for d in self.flow_history]
        flows = [d['flow'] for d in self.flow_history]
        stages = [d['stage'] for d in self.flow_history]
        
        # 流量曲線
        self.ax4.plot(times, flows, 'b-', linewidth=2, label='流量')
        
        # 段數背景色
        stage_colors = {1: 'lightblue', 2: 'orange', 3: 'red'}
        for i in range(len(times) - 1):
            stage = stages[i]
            self.ax4.axvspan(times[i], times[i+1], alpha=0.2, color=stage_colors[stage])
        
        # 流量段數標籤
        self.ax4.text(0.02, 0.98, '第1段: 0.4 cm³/s\n第2段: 0.6 cm³/s\n第3段: 0.8 cm³/s', 
                     transform=self.ax4.transAxes, verticalalignment='top',
                     bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))
        
        self.ax4.legend()
    
    def run_demo(self):
        """運行演示"""
        print("▶️  開始邊緣塗膠作業...")
        
        # 創建動畫
        ani = animation.FuncAnimation(
            self.fig, self.update_animation, 
            frames=len(self.height_data) + 10,  # 多幾幀用於顯示完成狀態
            interval=100,  # 100ms間隔
            repeat=False
        )
        
        plt.tight_layout()
        plt.show()
        
        return ani

def main():
    """主函數"""
    print("🎮 BLU邊緣塗膠PID控制視覺化演示")
    print("按 Ctrl+C 可隨時退出")
    
    try:
        demo = BLUVisualizationDemo()
        ani = demo.run_demo()
        
        # 保持動畫運行
        plt.show()
        
    except KeyboardInterrupt:
        print("\n⏹️  演示已停止")
    except Exception as e:
        print(f"❌ 演示失敗: {e}")
        logger.error(f"演示執行失敗: {e}")

if __name__ == "__main__":
    main()
