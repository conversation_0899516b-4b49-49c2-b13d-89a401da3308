"""
表面平整度監控系統
實時監控膠水表面平整度，避免塌陷導致玻璃貼合問題
"""

import numpy as np
import cv2
import time
import logging
from typing import Dict, List, Tuple, Optional, Callable
from dataclasses import dataclass
from enum import Enum
import threading
from collections import deque
import matplotlib.pyplot as plt

logger = logging.getLogger(__name__)

class MonitoringMode(Enum):
    """監控模式"""
    REAL_TIME = "real_time"
    BATCH = "batch"
    CALIBRATION = "calibration"

class AlertLevel(Enum):
    """警報等級"""
    INFO = "info"
    WARNING = "warning"
    CRITICAL = "critical"
    EMERGENCY = "emergency"

@dataclass
class SurfaceMetrics:
    """表面指標"""
    timestamp: float
    flatness_deviation: float  # 平整度偏差 (mm)
    surface_roughness: float   # 表面粗糙度 (mm)
    height_variance: float     # 高度變異 (mm²)
    slope_consistency: float   # 斜率一致性 (0-1)
    adhesive_thickness: float  # 膠水厚度 (mm)
    temperature: float         # 溫度 (°C)
    humidity: float           # 濕度 (%)

@dataclass
class Alert:
    """警報"""
    timestamp: float
    level: AlertLevel
    message: str
    location: Optional[Tuple[float, float]]
    metric_value: float
    threshold: float

class SurfaceMonitor:
    """表面監控器"""
    
    def __init__(self, flatness_threshold: float = 0.05, sampling_rate: float = 10.0):
        """
        初始化表面監控器
        
        Args:
            flatness_threshold: 平整度閾值 (mm)
            sampling_rate: 採樣率 (Hz)
        """
        self.flatness_threshold = flatness_threshold
        self.sampling_rate = sampling_rate
        self.sampling_interval = 1.0 / sampling_rate
        
        # 監控狀態
        self.is_monitoring = False
        self.monitoring_thread: Optional[threading.Thread] = None
        
        # 數據存儲
        self.metrics_history = deque(maxlen=1000)  # 保存最近1000個測量
        self.alerts = deque(maxlen=100)  # 保存最近100個警報
        
        # 閾值設定
        self.thresholds = {
            'flatness_deviation': flatness_threshold,
            'surface_roughness': 0.02,
            'height_variance': 0.01,
            'slope_consistency': 0.7,
            'temperature_range': (20, 30),  # °C
            'humidity_range': (40, 60)      # %
        }
        
        # 回調函數
        self.sensor_callback: Optional[Callable] = None
        self.alert_callback: Optional[Callable] = None
        
        logger.info(f"表面監控器初始化完成 - 平整度閾值: {flatness_threshold}mm, 採樣率: {sampling_rate}Hz")
    
    def set_callbacks(self, sensor_cb: Callable, alert_cb: Optional[Callable] = None):
        """設置回調函數"""
        self.sensor_callback = sensor_cb
        self.alert_callback = alert_cb
        logger.info("監控回調函數設置完成")
    
    def start_monitoring(self, mode: MonitoringMode = MonitoringMode.REAL_TIME):
        """開始監控"""
        if self.is_monitoring:
            logger.warning("監控已在運行中")
            return
        
        if not self.sensor_callback:
            logger.error("未設置感測器回調函數")
            return
        
        self.is_monitoring = True
        self.monitoring_thread = threading.Thread(
            target=self._monitoring_loop,
            args=(mode,),
            daemon=True
        )
        self.monitoring_thread.start()
        
        logger.info(f"開始表面監控 - 模式: {mode.value}")
    
    def stop_monitoring(self):
        """停止監控"""
        self.is_monitoring = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5.0)
        
        logger.info("表面監控已停止")
    
    def _monitoring_loop(self, mode: MonitoringMode):
        """監控循環"""
        logger.info("監控循環開始")
        
        while self.is_monitoring:
            try:
                start_time = time.time()
                
                # 獲取感測器數據
                sensor_data = self._get_sensor_data()
                if sensor_data is None:
                    time.sleep(self.sampling_interval)
                    continue
                
                # 計算表面指標
                metrics = self._calculate_surface_metrics(sensor_data)
                
                # 存儲數據
                self.metrics_history.append(metrics)
                
                # 檢查警報條件
                alerts = self._check_alert_conditions(metrics)
                for alert in alerts:
                    self.alerts.append(alert)
                    self._handle_alert(alert)
                
                # 控制採樣率
                elapsed = time.time() - start_time
                sleep_time = max(0, self.sampling_interval - elapsed)
                time.sleep(sleep_time)
                
            except Exception as e:
                logger.error(f"監控循環錯誤: {e}")
                time.sleep(1.0)
        
        logger.info("監控循環結束")
    
    def _get_sensor_data(self) -> Optional[Dict]:
        """獲取感測器數據"""
        try:
            if self.sensor_callback:
                data = self.sensor_callback("get_surface_data")
                return data
            return None
        except Exception as e:
            logger.error(f"獲取感測器數據失敗: {e}")
            return None
    
    def _calculate_surface_metrics(self, sensor_data: Dict) -> SurfaceMetrics:
        """計算表面指標"""
        try:
            height_map = sensor_data.get('height_map', np.array([]))
            coordinates = sensor_data.get('coordinates', np.array([]))
            temperature = sensor_data.get('temperature', 25.0)
            humidity = sensor_data.get('humidity', 50.0)
            
            if len(height_map) == 0:
                # 返回默認值
                return SurfaceMetrics(
                    timestamp=time.time(),
                    flatness_deviation=0.0,
                    surface_roughness=0.0,
                    height_variance=0.0,
                    slope_consistency=1.0,
                    adhesive_thickness=0.0,
                    temperature=temperature,
                    humidity=humidity
                )
            
            # 計算平整度偏差
            mean_height = np.mean(height_map)
            flatness_deviation = np.max(height_map) - np.min(height_map)
            
            # 計算表面粗糙度 (Ra)
            surface_roughness = np.mean(np.abs(height_map - mean_height))
            
            # 計算高度變異
            height_variance = np.var(height_map)
            
            # 計算斜率一致性
            slope_consistency = self._calculate_slope_consistency(height_map, coordinates)
            
            # 估算膠水厚度（基於高度數據）
            adhesive_thickness = np.mean(height_map)
            
            metrics = SurfaceMetrics(
                timestamp=time.time(),
                flatness_deviation=flatness_deviation,
                surface_roughness=surface_roughness,
                height_variance=height_variance,
                slope_consistency=slope_consistency,
                adhesive_thickness=adhesive_thickness,
                temperature=temperature,
                humidity=humidity
            )
            
            return metrics
            
        except Exception as e:
            logger.error(f"計算表面指標失敗: {e}")
            # 返回錯誤指標
            return SurfaceMetrics(
                timestamp=time.time(),
                flatness_deviation=999.0,
                surface_roughness=999.0,
                height_variance=999.0,
                slope_consistency=0.0,
                adhesive_thickness=0.0,
                temperature=25.0,
                humidity=50.0
            )
    
    def _calculate_slope_consistency(self, height_map: np.ndarray, coordinates: np.ndarray) -> float:
        """計算斜率一致性"""
        if len(coordinates) < 3:
            return 1.0
        
        try:
            slopes = []
            for i in range(len(coordinates) - 1):
                dx = coordinates[i+1, 0] - coordinates[i, 0]
                dy = coordinates[i+1, 1] - coordinates[i, 1]
                dh = height_map[i+1] - height_map[i]
                
                if dx != 0 or dy != 0:
                    distance_2d = np.sqrt(dx**2 + dy**2)
                    slope = dh / distance_2d
                    slopes.append(slope)
            
            if not slopes:
                return 1.0
            
            # 計算斜率的一致性（基於標準差）
            slope_std = np.std(slopes)
            consistency = max(0.0, 1.0 - slope_std * 10)  # 標準差越小，一致性越高
            
            return consistency
            
        except Exception as e:
            logger.error(f"計算斜率一致性失敗: {e}")
            return 0.0
    
    def _check_alert_conditions(self, metrics: SurfaceMetrics) -> List[Alert]:
        """檢查警報條件"""
        alerts = []
        
        # 檢查平整度偏差
        if metrics.flatness_deviation > self.thresholds['flatness_deviation']:
            level = AlertLevel.CRITICAL if metrics.flatness_deviation > self.thresholds['flatness_deviation'] * 2 else AlertLevel.WARNING
            alerts.append(Alert(
                timestamp=metrics.timestamp,
                level=level,
                message=f"平整度偏差超標: {metrics.flatness_deviation:.3f}mm (閾值: {self.thresholds['flatness_deviation']:.3f}mm)",
                location=None,
                metric_value=metrics.flatness_deviation,
                threshold=self.thresholds['flatness_deviation']
            ))
        
        # 檢查表面粗糙度
        if metrics.surface_roughness > self.thresholds['surface_roughness']:
            alerts.append(Alert(
                timestamp=metrics.timestamp,
                level=AlertLevel.WARNING,
                message=f"表面粗糙度過高: {metrics.surface_roughness:.3f}mm",
                location=None,
                metric_value=metrics.surface_roughness,
                threshold=self.thresholds['surface_roughness']
            ))
        
        # 檢查斜率一致性
        if metrics.slope_consistency < self.thresholds['slope_consistency']:
            alerts.append(Alert(
                timestamp=metrics.timestamp,
                level=AlertLevel.WARNING,
                message=f"斜率一致性不足: {metrics.slope_consistency:.2f}",
                location=None,
                metric_value=metrics.slope_consistency,
                threshold=self.thresholds['slope_consistency']
            ))
        
        # 檢查溫度範圍
        temp_min, temp_max = self.thresholds['temperature_range']
        if not (temp_min <= metrics.temperature <= temp_max):
            alerts.append(Alert(
                timestamp=metrics.timestamp,
                level=AlertLevel.WARNING,
                message=f"溫度超出範圍: {metrics.temperature:.1f}°C (範圍: {temp_min}-{temp_max}°C)",
                location=None,
                metric_value=metrics.temperature,
                threshold=temp_max if metrics.temperature > temp_max else temp_min
            ))
        
        return alerts
    
    def _handle_alert(self, alert: Alert):
        """處理警報"""
        logger.log(
            logging.CRITICAL if alert.level == AlertLevel.CRITICAL else logging.WARNING,
            f"表面監控警報 [{alert.level.value.upper()}]: {alert.message}"
        )
        
        # 調用警報回調
        if self.alert_callback:
            try:
                self.alert_callback(alert)
            except Exception as e:
                logger.error(f"警報回調執行失敗: {e}")
    
    def get_current_status(self) -> Dict:
        """獲取當前狀態"""
        if not self.metrics_history:
            return {"status": "no_data", "message": "無監控數據"}
        
        latest_metrics = self.metrics_history[-1]
        recent_alerts = [alert for alert in self.alerts if time.time() - alert.timestamp < 300]  # 最近5分鐘的警報
        
        status = {
            "monitoring_active": self.is_monitoring,
            "latest_metrics": {
                "timestamp": latest_metrics.timestamp,
                "flatness_deviation": latest_metrics.flatness_deviation,
                "surface_roughness": latest_metrics.surface_roughness,
                "slope_consistency": latest_metrics.slope_consistency,
                "temperature": latest_metrics.temperature,
                "humidity": latest_metrics.humidity
            },
            "thresholds": self.thresholds,
            "recent_alerts_count": len(recent_alerts),
            "critical_alerts_count": len([a for a in recent_alerts if a.level == AlertLevel.CRITICAL]),
            "data_points": len(self.metrics_history)
        }
        
        return status
    
    def generate_quality_report(self, time_range_minutes: int = 60) -> Dict:
        """生成品質報告"""
        cutoff_time = time.time() - (time_range_minutes * 60)
        recent_metrics = [m for m in self.metrics_history if m.timestamp >= cutoff_time]
        
        if not recent_metrics:
            return {"error": "指定時間範圍內無數據"}
        
        flatness_values = [m.flatness_deviation for m in recent_metrics]
        roughness_values = [m.surface_roughness for m in recent_metrics]
        consistency_values = [m.slope_consistency for m in recent_metrics]
        
        report = {
            "time_range_minutes": time_range_minutes,
            "total_measurements": len(recent_metrics),
            "flatness_analysis": {
                "mean": np.mean(flatness_values),
                "std": np.std(flatness_values),
                "max": np.max(flatness_values),
                "within_spec_rate": sum(1 for f in flatness_values if f <= self.thresholds['flatness_deviation']) / len(flatness_values)
            },
            "roughness_analysis": {
                "mean": np.mean(roughness_values),
                "std": np.std(roughness_values),
                "max": np.max(roughness_values)
            },
            "consistency_analysis": {
                "mean": np.mean(consistency_values),
                "min": np.min(consistency_values)
            },
            "alert_summary": {
                "total_alerts": len([a for a in self.alerts if a.timestamp >= cutoff_time]),
                "critical_alerts": len([a for a in self.alerts if a.timestamp >= cutoff_time and a.level == AlertLevel.CRITICAL])
            }
        }
        
        return report
