"""
BLU邊緣塗膠智能點膠系統
用於處理BLU底部高低起伏的邊緣膠水塗佈，確保表面平整度
"""

import numpy as np
import logging
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from enum import Enum

# 配置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DispensingMode(Enum):
    """點膠模式"""
    EDGE_COATING = "edge_coating"  # 邊緣塗膠
    SURFACE_LEVELING = "surface_leveling"  # 表面平整
    COMPENSATION = "compensation"  # 高度補償

@dataclass
class DispensingSpec:
    """點膠規格參數"""
    width: float = 1.4  # 膠水寬度 (mm)
    height: float = 1.2  # 膠水高度 (mm)
    tolerance: float = 0.2  # 公差 (±mm)
    surface_flatness_requirement: float = 0.05  # 表面平整度要求 (mm)

@dataclass
class HeightProfile:
    """高度輪廓數據"""
    x_coordinates: np.ndarray
    y_coordinates: np.ndarray
    height_values: np.ndarray
    edge_coordinates: List[Tuple[float, float]]

@dataclass
class DispensingPoint:
    """點膠點數據"""
    x: float
    y: float
    z_target: float  # 目標高度
    volume: float  # 膠水體積
    compensation_factor: float  # 補償係數

class BLUEdgeDispensingSystem:
    """BLU邊緣塗膠系統主類"""
    
    def __init__(self, spec: DispensingSpec):
        self.spec = spec
        self.height_profile: Optional[HeightProfile] = None
        self.dispensing_points: List[DispensingPoint] = []
        self.surface_quality_data: Dict = {}
        
        logger.info(f"初始化BLU邊緣塗膠系統，規格: 寬度{spec.width}mm, 高度{spec.height}mm, 公差±{spec.tolerance}mm")
    
    def load_height_profile(self, height_data: np.ndarray, coordinates: np.ndarray) -> bool:
        """
        載入BLU底部高度輪廓數據
        
        Args:
            height_data: 高度數據陣列
            coordinates: 座標數據 (x, y)
            
        Returns:
            bool: 載入是否成功
        """
        try:
            if height_data.shape[0] != coordinates.shape[0]:
                raise ValueError("高度數據與座標數據長度不匹配")
            
            # 檢測邊緣座標
            edge_coords = self._detect_edge_coordinates(coordinates, height_data)
            
            self.height_profile = HeightProfile(
                x_coordinates=coordinates[:, 0],
                y_coordinates=coordinates[:, 1],
                height_values=height_data,
                edge_coordinates=edge_coords
            )
            
            logger.info(f"成功載入高度輪廓數據，共{len(height_data)}個測量點，{len(edge_coords)}個邊緣點")
            return True
            
        except Exception as e:
            logger.error(f"載入高度輪廓數據失敗: {e}")
            return False
    
    def _detect_edge_coordinates(self, coordinates: np.ndarray, height_data: np.ndarray) -> List[Tuple[float, float]]:
        """檢測BLU邊緣座標"""
        # 簡化的邊緣檢測算法，實際應用中可能需要更複雜的算法
        x_coords = coordinates[:, 0]
        y_coords = coordinates[:, 1]
        
        # 找到邊界點
        x_min, x_max = np.min(x_coords), np.max(x_coords)
        y_min, y_max = np.min(y_coords), np.max(y_coords)
        
        edge_points = []
        tolerance = 0.1  # 邊緣檢測容差
        
        for i, (x, y) in enumerate(coordinates):
            # 檢查是否為邊緣點
            if (abs(x - x_min) < tolerance or abs(x - x_max) < tolerance or 
                abs(y - y_min) < tolerance or abs(y - y_max) < tolerance):
                edge_points.append((x, y))
        
        return edge_points
    
    def calculate_compensation_algorithm(self) -> bool:
        """
        計算高度補償算法
        分析底部起伏並計算所需的膠水補償量
        """
        if self.height_profile is None:
            logger.error("未載入高度輪廓數據")
            return False
        
        try:
            # 計算目標平面高度（取最高點作為參考）
            target_height = np.max(self.height_profile.height_values)
            
            # 為每個邊緣點計算補償
            self.dispensing_points = []
            
            for edge_x, edge_y in self.height_profile.edge_coordinates:
                # 找到最接近的測量點
                distances = np.sqrt((self.height_profile.x_coordinates - edge_x)**2 + 
                                  (self.height_profile.y_coordinates - edge_y)**2)
                closest_idx = np.argmin(distances)
                current_height = self.height_profile.height_values[closest_idx]
                
                # 計算需要補償的高度
                height_deficit = target_height - current_height
                
                # 計算膠水體積（考慮規格要求）
                base_volume = self.spec.width * self.spec.height  # 基礎體積
                compensation_volume = height_deficit * self.spec.width  # 補償體積
                total_volume = base_volume + compensation_volume
                
                # 計算補償係數
                compensation_factor = 1.0 + (height_deficit / self.spec.height)
                
                dispensing_point = DispensingPoint(
                    x=edge_x,
                    y=edge_y,
                    z_target=target_height,
                    volume=total_volume,
                    compensation_factor=compensation_factor
                )
                
                self.dispensing_points.append(dispensing_point)
            
            logger.info(f"計算完成，生成{len(self.dispensing_points)}個點膠點")
            return True
            
        except Exception as e:
            logger.error(f"補償算法計算失敗: {e}")
            return False
    
    def validate_surface_flatness(self) -> Dict[str, float]:
        """
        驗證表面平整度
        確保膠水表面符合平整度要求
        """
        if not self.dispensing_points:
            logger.warning("無點膠點數據，無法驗證表面平整度")
            return {}
        
        # 計算預期的表面高度分佈
        target_heights = [point.z_target for point in self.dispensing_points]
        height_variance = np.var(target_heights)
        height_std = np.std(target_heights)
        max_deviation = np.max(target_heights) - np.min(target_heights)
        
        flatness_metrics = {
            "height_variance": height_variance,
            "height_std": height_std,
            "max_deviation": max_deviation,
            "flatness_requirement": self.spec.surface_flatness_requirement,
            "meets_requirement": max_deviation <= self.spec.surface_flatness_requirement
        }
        
        self.surface_quality_data = flatness_metrics
        
        logger.info(f"表面平整度驗證完成，最大偏差: {max_deviation:.3f}mm, "
                   f"要求: {self.spec.surface_flatness_requirement}mm, "
                   f"符合要求: {flatness_metrics['meets_requirement']}")
        
        return flatness_metrics
    
    def get_dispensing_plan(self) -> Dict:
        """
        獲取完整的點膠計劃
        """
        if not self.dispensing_points:
            logger.warning("尚未計算點膠點")
            return {}
        
        plan = {
            "total_points": len(self.dispensing_points),
            "specification": {
                "width": self.spec.width,
                "height": self.spec.height,
                "tolerance": self.spec.tolerance
            },
            "dispensing_points": [
                {
                    "position": (point.x, point.y),
                    "target_height": point.z_target,
                    "volume": point.volume,
                    "compensation_factor": point.compensation_factor
                }
                for point in self.dispensing_points
            ],
            "surface_quality": self.surface_quality_data
        }
        
        return plan
